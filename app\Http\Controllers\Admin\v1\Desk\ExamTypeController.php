<?php

namespace App\Http\Controllers\Admin\v1\Desk;

use App\Models\ExamTypeModel;
use App\Services\Desk\ExamTypeService;
use Illuminate\Http\Request;

class ExamTypeController
{
    /**
     * describe：考试类型列表
     * list
     * @param Request $request
     * @return null
     * 2025/6/13 - Mark
     */
    public function list(Request $request)
    {
        $list = (new ExamTypeModel())->when($request->name, function ($query) use ($request) {
            $query->where('name', 'like', "%{$request->name}%");
        })
            ->orderBy('created_at', 'desc')
            ->paginate($request->limit ?? 10);
        $paginate = [
            'page_size' => $request->limit ?? 10,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];
        return responseSuccess(compact('list', 'paginate'));
    }

    /**
     * describe：添加考试类型
     * add
     * @param Request $request
     * @return null
     * 2025/6/13 - Mark
     */
    public function add(Request $request, ExamTypeService $examTypeService)
    {
        $data = $request->validate([
            'name' => 'required',
            'project_id' => 'required|exists:mysql_ai_desk.cna_project_service_data,id',
            'course_id' => 'required|exists:mysql_ai_desk.cna_project_service_course,id'
        ]);
        $info = $examTypeService->add($data);
        return $info;
    }

    /**
     * describe：删除考试类型
     * delete
     * @param Request $request
     * @return null
     * 2025/6/13 - Mark
     */
    public function delete(Request $request, ExamTypeService $examTypeService)
    {
        $data = $request->validate([
            'id' => 'required',
        ]);
        $info = $examTypeService->delete($data);
        return $info;
    }

    /**
     * describe：修改考试类型
     * edit
     * @param Request $request
     * @return null
     * 2025/6/13 - Mark
     */
    public function edit(Request $request, ExamTypeService $examTypeService)
    {
        $data = $request->validate([
            'id' => 'required',
            'name' => 'required',
            'project_id' => 'required|exists:mysql_ai_desk.cna_project_service_data,id',
            'course_id' => 'required|exists:mysql_ai_desk.cna_project_service_course,id',
            'status' => 'nullable'
        ]);
        $info = $examTypeService->edit($data);
        return $info;
    }

    /**
     * describe：考试类型详情
     * info
     * @param Request $request
     * @return null
     * 2025/6/13 - Mark
     */
    public function info($id)
    {
        $info = ExamTypeModel::where('id', $id)->first();
        if (!$info) {
            return responseFail('数据不存在');
        }
        return responseSuccess($info, '请求成功');
    }

}