<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ExamQuestionsModel extends Model
{
    use SoftDeletes;

    protected $connection = 'mysql_ai_desk';
    protected $table = 'cna_exam_questions';

    const TYPE_RADIO = 0;
    const TYPE_CHECKBOX = 1;

    const TYPE_TXT = 2;
    const TYPE_JUDGE = 3;

    const TYPE_ALL = [self::TYPE_RADIO, self::TYPE_CHECKBOX, self::TYPE_TXT, self::TYPE_JUDGE];

    const TYPE_REMARK = [
        self::TYPE_RADIO => '单选题',
        self::TYPE_CHECKBOX => '多选题',
        self::TYPE_TXT => '文本',
        self::TYPE_JUDGE => '判断题',
    ];

    //追加字段
    protected $appends = ['type_remark'];

    //获取追加字段 题目类型
    public function getTypeRemarkAttribute()
    {
        return self::TYPE_REMARK[$this->type] ?? '未知类型';
    }

    //指定数组字段
    protected $casts = [
        'option' => 'array', // 将 option 字段自动转换为数组
    ];


    protected $fillable = ['exam_id', 'type', 'question', 'option', 'score'];

    public function exam(){
        return $this->belongsTo(ExamTypeModel::class, 'exam_id');
    }

    protected function serializeDate(\DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s'); // 自定义时间格式
    }
}

