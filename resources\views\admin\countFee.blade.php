<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>计算公式</title>
</head>
<body>



<div style="display: flex; align-items: center; flex-direction: column">
    <h2>计算公式</h2>
    <div class="form">
        <form action="/api/v1/admin/countFee" method="post">

            <!-- 下拉选择框 -->
            <div class="row">
                <label for="roleid">角&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;色:</label>
                <select id="roleid" name="roleid">
                    @foreach ($roleData as $key => $val)
                        <option value="{{$key}}" @if (isset($param['roleid']) && $param['roleid']==$key) selected @endif >{{$val}}</option>
                    @endforeach
                </select>
            </div>

            <div id="grade"   style="display: none">
                <label for="rank" style="margin-right: 18px">三三制等级:</label>
                <select id="rank" name="rank">
                    @foreach ($roleRank as $key => $val)
                        <option value="{{$key}}" @if (isset($param['rank']) && $param['rank']==$key) selected @endif>{{$val}}</option>
                    @endforeach
                </select>
            </div>

            <div id="firms"   style="display: none">
                <label for="rank" style="margin-right: 18px">联号事务所区域:</label>
                <select id="rank1" name="rank1">
                    @foreach ($firmsRank as $key => $val)
                        <option value="{{$key}}" @if (isset($param['rank']) && $param['rank']==$key) selected @endif>{{$val}}</option>
                    @endforeach
                </select>
            </div>


            <div class="row">
                <label for="case">服务项目:</label>
                <select id="case" name="case">
                    @foreach ($cases as $key => $val)
                        <option value="{{$key}}" @if (isset($param['case']) && $param['case']==$key) selected @endif>{{$val}}</option>
                    @endforeach
                    <!-- 更多选项 -->
                </select>
            </div>

            <div class="row">
                <label for="amount">金&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;额:</label>
                <input type="text" id="amount" name="amount" required @if (isset($param['amount'])) value="{{$param['amount']}}" @endif>
            </div>



            <!-- 提交按钮 -->
            <div>
                <input type="submit" value="提交">
            </div>

        </form>
    </div>

    <div class="content">

        <div class="left">
            <table border="1" width="700">
                <tr>
                    <td>职位</td>
                    <td>A</td>
                    <td>B</td>
                    <td>C</td>
                    <td>D</td>
                    <td>E</td>
                    <td>E*2</td>
                </tr>
                @foreach ($teamData_init as $key => $item)
                    <tr>
                        <td>{{$item['remark']}}</td>
                        <td>{{$item['caseA']}}%</td>
                        <td>{{$item['caseB']}}%</td>
                        <td>{{$item['caseC']}}%</td>
                        <td>{{$item['caseD'] }}%</td>
                        <td>{{$item['caseE'] }}%</td>
                        <td>{{$item['caseE2'] }}%</td>
                    </tr>
                @endforeach
                <tr>
                    <td colspan="7">三三制</td>
                </tr>
                @foreach ($data_init as $key => $item)
                    <tr>
                        <td>{{$item['remark']}}</td>
                        <td>{{$item['caseA']}}%</td>
                        <td>{{$item['caseB']}}%</td>
                        <td>{{$item['caseC']}}%</td>
                        <td>{{$item['caseD'] }}%</td>
                        <td>{{$item['caseE'] }}%</td>
                        <td>{{$item['caseE2'] }}%</td>
                    </tr>
                @endforeach
                <tr>
                    <td colspan="7">联号事务所</td>
                </tr>
                @foreach ($firmsData_init as $key => $item)
                    <tr>
                        <td>{{$item['remark']}}</td>
                        <td>{{$item['caseA']}}%</td>
                        <td>{{$item['caseB']}}%</td>
                        <td>{{$item['caseC']}}%</td>
                        <td>{{$item['caseD'] }}%</td>
                        <td>{{$item['caseE'] }}%</td>
                        <td>{{$item['caseE2'] }}%</td>
                    </tr>
                @endforeach

            </table>
        </div>

        <div class="right">
            {{--三三制分开显示--}}
          {{--  @if (isset($param['roleid']) && $param['roleid']==3)

                <table border="1" width="600">
                    <tr>
                        <td>服务项目</td>
                        <td>津贴</td>
                        <td>级别</td>
                        <td>金额</td>
                        <td>比例</td>
                        <td>收入</td>
                    </tr>

                    @foreach ($teamData as $key => $item)
                        <tr>
                            <td>{{$param['case_name']}}</td>
                            <td>{{$item['jintie']}}</td>
                            <td>{{$item['name']}}</td>
                            <td>{{$param['amount_name']}}</td>
                            <td>{{ $item['key'] }}%</td>
                            <td>{{ $item['value'] }}元</td>
                        </tr>
                    @endforeach

                </table>
                <br><br>
            @endif--}}


            @if(isset($data))

                <table border="1" width="700">
                    <tr>
                        <td>服务项目</td>
                        <td>津贴</td>
                        <td>级别</td>
                        <td>金额</td>
                        <td>比例</td>
                        <td>收入</td>
                    </tr>
                    @if (isset($param['roleid']) && $param['roleid']==3)
                        @foreach ($teamData as $key => $item)
                            <tr>
                                <td>{{$param['case_name']}}</td>
                                <td>{{$item['jintie']}}</td>
                                <td>{{$item['name']}}</td>
                                <td>{{$param['amount_name']}}</td>
                                <td>{{ $item['key'] }}%</td>
                                <td>{{ $item['value'] }}元</td>
                            </tr>
                        @endforeach

                    @endif
                    @foreach ($data as $key => $item)
                        <tr>
                            <td>{{$param['case_name']}}</td>
                            <td>{{$item['jintie']}}</td>
                            <td>{{$item['name']}}</td>
                            <td>{{$param['amount_name']}}</td>
                            <td>{{ $item['key'] }}%</td>
                            <td>{{ $item['value'] }}元</td>
                        </tr>
                    @endforeach

                </table>
            @endif
        </div>




    </div>

</div>


</body>
<style>
    .form{}
    .row{
        margin: 15px 0;
        display: flex;
    }
    .row label{
        width: 100px;
        display: block;
    }

    .content{
        margin-top:50px;

    }

    .content .left{

        margin-right: 20px;
        margin-bottom: 50px;
    }

    .content .left table td, .content .right table td{
        text-align: center;
    }

    .content .right{
        margin-bottom: 50px;
    }
</style>
<script>
    // 获取 select 元素和显示选择结果的元素
    var selectElement = document.getElementById('roleid');
    var selectedValueElement = document.getElementById('roleid');
    var selectedOption = selectElement.value;
    if (selectedOption == 3) { // 三三制
        document.getElementById('grade').style.display = 'inline-flex'
        document.getElementById('firms').style.display = 'none'
    } else if (selectedOption == 4) { // 联号事务所
        document.getElementById('firms').style.display = 'inline-flex'
        document.getElementById('grade').style.display = 'none'
    } else {
        document.getElementById('grade').style.display = 'none'
        document.getElementById('firms').style.display = 'none'
    }


    // 为 select 元素添加 change 事件监听器
    selectElement.addEventListener('change', function() {
        // 获取选中的选项值
        selectedOption = this.value;
        if (selectedOption == 3) {  // 三三制
            document.getElementById('grade').style.display = 'inline-flex'
            document.getElementById('firms').style.display = 'none'
        } else if (selectedOption == 4) { // 联号事务所
            document.getElementById('firms').style.display = 'inline-flex'
            document.getElementById('grade').style.display = 'none'
        } else {
            document.getElementById('grade').style.display = 'none'
            document.getElementById('firms').style.display = 'none'
        }
    });
</script>
</html>