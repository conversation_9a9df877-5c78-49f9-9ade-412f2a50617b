<?php

namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\VisitorApply;
use App\Models\VisitorAttach;
use App\Models\VisitorPeople;
use App\Services\OssService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class VisitorApplyController extends Controller
{

    /**
     * 到访申请表
     * @param Request $request
     * @return null
     */
    public function index(Request $request)
    {
        $user = $request->attributes->get('user');
        $keyword = $request->get('keyword', '');
        $pageSize = $request->get('page_size', 10);
        $status = $request->get('status', '');
        $start_date = $request->get('start_date', '');;  // 开始时间
        $end_date = $request->get('end_date', '');;  // 结束时间

        // 是否固定邀请对像
        $phone = '';
        if (!in_array($user['profileEmail'], VisitorPeople::ALLDATA)) {
            $peopleId = VisitorPeople::where('account', $user['profileEmail'])->value('id');
            if ($peopleId) {
                $phone = $peopleId;
            } else {
                $phone = $user['profileContact'];
            }
        }


        $list = VisitorApply::query()
            ->when($phone != '', function($q) use ($phone) {
                $q->whereRaw('FIND_IN_SET('.$phone.', invite_phone)');
            })->when($keyword != '', function ($q) use ($keyword) {

                $q->where(function ($q) use ($keyword) {
                    $q->orWhere('name', 'like', '%' . $keyword . '%')
                        ->orWhere('phone', 'like', '%' . $keyword . '%')
                        ->orWhere('invite_people', 'like', '%' . $keyword . '%');
                });

             })->when($start_date !='', function ($q) use ($start_date) {
                $q->where('visitor_date', '>=', $start_date);
            })->when($end_date !='', function ($q) use ($end_date) {
                $q->where('visitor_date', '<=', $end_date);
            })->when($status !='', function ($q) use ($status) {
                $q->where('status', $status);
            })
            ->orderBy('id', 'desc')
            ->paginate($pageSize);

        $items = $list->items();

        $items = collect($items)->map(function ($item) {
            $item->avatar = $item->avatar ? OssService::link($item->avatar) : null;
            return $item;
        });


        $paginate = [
            'page_size' => $pageSize,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate'));
    }

    /**
     * 到访申请详情
     * @param Request $request
     * @return void
     */
    public function detail(Request $request)
    {
        $id = $request->input('id');
        if (empty($id)) {
            return responseFail(__('missing parameter', ['param' => 'id']));
        }

        // 查看
        $data = VisitorApply::where('id', $id)->first();
        if ($data && $data['avatar']) {
            $data['avatar'] = OssService::link($data['avatar']);
        }

        // 企业附件
        $arrAttach = [];
        $attachs = VisitorAttach::query()->where('obj_id', $id)->where('type', 1)->get()->toArray();
        if ($attachs) {
            foreach ($attachs as $attach) {
                $arrAttach[] = OssService::link($attach['file_path']);;
            }
            $data['attach'] = $arrAttach;
        }

        return responseSuccess($data);
    }


    // 申批
    public function check(Request $request)
    {
        $id = $request->input('id');
        $status = $request->input('status', 0);
        $reject_reason = $request->input('reject_reason', '');

        if (empty($id)) {
            return responseFail(__('missing parameter', ['param' => 'id']));
        }

        if (empty($status)) {
            return responseFail(__('missing parameter', ['param' => 'status']));
        }

        if (!in_array($status, [1,2])) {
            return responseFail(__('param error', ['param' => 'status']));
        }

        if ($status == 2 && empty($reject_reason)) {
            return responseFail(__('missing parameter', ['param' => 'reject_reason']));
        }


        // 查看详情
        $info = VisitorApply::where('id', $id)->first();
        if (empty($info)) {
            return responseFail(__('info no exist'));
        }

        // 是否审批过了
        if (in_array($info['status'], [1,2])) {
            return responseFail(__('param error', ['param' => 'status']));
        }

        VisitorApply::where('id', $id)->update([
            'status' => $status,
            'reject_reason' => $reject_reason,
        ]);

        return responseSuccess();

    }

    /**
     * 下载邀请卡
     * @param Request $request
     * @return null
     */
    public function downloadCard(Request $request)
    {
        $body = $request->validate([
            'id' => ['required', 'integer', 'min:1'],
            'type' => ['required', 'string', Rule::in(['cn', 'en'])]
        ]);
        $record = VisitorApply::select('invite_people', 'visitor', 'visitor_date', 'visitor_time', 'numbers', 'status')->find($body['id']);
        if (empty($record)) {
            return responseFail(__('info no exist'));
        }

        // 加载图片
        $path = storage_path('pdf/visitor-card-'.$body['type'].'.jpg');
        $image = imagecreatefromjpeg($path); // 替换为你的图片路径

        // 分配颜色
        $color = imagecolorallocate($image, 255, 0, 0); // 红色字体

        // 设置字体路径
        $font = storage_path('app/public/fonts/simfang.ttf');; // 替换为你的字体文件路径

        // 在图片上写字
        imagettftext($image, 20, 0, 10, 50, $color, $font, 'Hello, World!');

        // 保存路径是否存在
        $directory = storage_path('app/public/visitor/card/');
        if (!is_dir($directory)) {
            mkdir($directory, 0777, true);
        }
        // 保存到本地
        $filename = $body['id'].'.jpg';
        $outputPath = $directory.$filename;
        if (imagejpeg($image, $outputPath)) {
            // 释放内存
            imagedestroy($image);
            return Storage::disk('public')->download('/visitor/card/'. $filename);
        } else {
            return responseFail();
        }



    }
}
