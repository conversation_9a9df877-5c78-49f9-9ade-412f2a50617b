<?php

use App\Http\Controllers\Admin\v1\ExamQuestionsController;
use App\Http\Controllers\Admin\v1\ActiveLogController;
use App\Http\Controllers\Admin\v1\ActivityController;
use App\Http\Controllers\Admin\v1\ActivitySignupController;
use App\Http\Controllers\Admin\v1\BenefitInfoController;
use App\Http\Controllers\Admin\v1\BenefitUserController;
use App\Http\Controllers\Admin\v1\BillController;
use App\Http\Controllers\Admin\v1\DocumentFoldersController;
use App\Http\Controllers\Admin\v1\EmailTemplateController;
use App\Http\Controllers\Admin\v1\FirmController;
use App\Http\Controllers\Admin\v1\GspController;
use App\Http\Controllers\Admin\v1\InterviewQuestionController;
use App\Http\Controllers\Admin\v1\InquiryCompanyController;
use App\Http\Controllers\Admin\v1\InquiryCopyrightController;
use App\Http\Controllers\Admin\v1\InquiryDishonestController;
use App\Http\Controllers\Admin\v1\InquiryExecutedController;
use App\Http\Controllers\Admin\v1\InquiryLawsController;
use App\Http\Controllers\Admin\v1\InquiryLitigationController;
use App\Http\Controllers\Admin\v1\InquiryPatentController;
use App\Http\Controllers\Admin\v1\InquiryProcessingController;
use App\Http\Controllers\Admin\v1\InquiryReportController;
use App\Http\Controllers\Admin\v1\CompanyController;
use App\Http\Controllers\Admin\v1\ConfigController;
use App\Http\Controllers\Admin\v1\DigitalHumanController;
use App\Http\Controllers\Admin\v1\DocumentInfoController;
use App\Http\Controllers\Admin\v1\FileController;
use App\Http\Controllers\Admin\v1\InquiryTrademarkController;
use App\Http\Controllers\Admin\v1\InvoiceController;
use App\Http\Controllers\Admin\v1\InvoiceDetailController;
use App\Http\Controllers\Admin\v1\LoginController;
use App\Http\Controllers\Admin\v1\LoginLogController;
use App\Http\Controllers\Admin\v1\NotificationController;
use App\Http\Controllers\Admin\v1\PaymentController;
use App\Http\Controllers\Admin\v1\PowerInfoController;
use App\Http\Controllers\Admin\v1\ProfileInfoController;
use App\Http\Controllers\Admin\v1\RemarkController;
use App\Http\Controllers\Admin\v1\SettingController;
use App\Http\Controllers\Admin\v1\TeamProfileController;
use App\Http\Controllers\Admin\v1\VisitorApplyController;
use App\Http\Controllers\Admin\v1\VisitorReportController;
use App\Http\Controllers\Admin\v1\VisitorSigninController;
use App\Http\Controllers\Admin\v1\Web\StructureController;
use App\Http\Controllers\Api\v1\Captcha\CaptchaController;
use Illuminate\Support\Facades\Route;

Route::group(['prefix' => 'v1/admin'], function () {

    // 用户登录
    Route::post('login', [LoginController::class, 'login']);
    // 退出
    Route::post('logout', [LoginController::class, 'logout']);
    // 图形验证码
    Route::get('captcha', [CaptchaController::class, 'captcha']);
    // 测试津贴计算
    Route::any('countFee', [TeamProfileController::class, 'countFee']);
    // 测试个税计算
    Route::any('countTax', [TeamProfileController::class, 'countTax']);
    // 测试平台交税
    Route::any('countPlat', [TeamProfileController::class, 'countPlat']);

    // 尽调报告
    Route::group(['prefix' => 'inquiry'], function () {
        Route::post('saveCompany', [InquiryCompanyController::class, 'store']); // 保存公司信息
        Route::post('saveCopyright', [InquiryCopyrightController::class, 'store']); // 保存公司版权信息
        Route::post('saveDishonest', [InquiryDishonestController::class, 'store']); // 保存失信信息
        Route::post('savePatent', [InquiryPatentController::class, 'store']); // 保存专利信息
        Route::post('saveTrademark', [InquiryTrademarkController::class, 'store']); // 保存商标信息
        Route::post('saveProcessing', [InquiryProcessingController::class, 'store']); // 保存行政处罚记录
        Route::post('saveExecuted', [InquiryExecutedController::class, 'store']); // 保存执行人记录
        Route::post('saveLaws', [InquiryLawsController::class, 'store']); // 保存国内外法律法规
    });


    //*********************api接口需要登录的**********************
    Route::group(['middleware' => 'admin.auth'], function () {

        // 公共配置
        Route::get('configurations', [ConfigController::class, 'configurations']); //配置列表
        Route::group(['prefix' => 'config'], function () {
            Route::get('structure', [ConfigController::class, 'structure']); //多语言翻译
            Route::get('countryList', [ConfigController::class, 'countryList']); //国家列表
            Route::get('mobilePrefixList', [ConfigController::class, 'mobilePrefixList']); //手机区号列表
            Route::get('bankList', [ConfigController::class, 'bankList']); //银行列表
            Route::get('countries', [ConfigController::class, 'countries']); //国家列表
        });

        // 登录行政用户的信息
        Route::get('profile', [LoginController::class, 'me']);

        // 行政用户初次登录设置
        Route::group(['prefix' => 'login'], function () {
            Route::post('setLanguage', [LoginController::class, 'setLanguage']); //语言设置
            Route::post('setSecurity', [LoginController::class, 'setSecurity']); //安全设置
            Route::post('setProfile', [LoginController::class, 'setProfile']); //用户设置
        });

        // 备注信息
        Route::group(['prefix' => 'remark'], function () {
            Route::get('index', [RemarkController::class, 'index']); // 获取备注列表
            Route::post('create', [RemarkController::class, 'store']); // 添加备注
            Route::get('edit/{id}', [RemarkController::class, 'edit']); // 备注详情
            Route::post('edit/{id}', [RemarkController::class, 'update']); // 修改备注
            Route::delete('delete/{id}', [RemarkController::class, 'destroy']); // 删除
        });

        // 合伙人
        Route::group(['prefix' => 'partner'], function () {
            Route::get('list', [ProfileInfoController::class, 'partnerList']); // 合伙人列表
            Route::get('info/{id}', [ProfileInfoController::class, 'partnerDetail']); // 合伙人详情
            Route::post('check', [ProfileInfoController::class, 'partnerCheck']); // 审核合伙人
            Route::post('edit/{id}', [ProfileInfoController::class, 'partnerEdit']); // 更新合伙人信息
            Route::post('setting/{id}', [ProfileInfoController::class, 'partnerSetting']); // 合伙人个人设置
            Route::post('setTeamTop', [ProfileInfoController::class, 'setTeamTop']); // 设置三三制总
            Route::post('changePre', [ProfileInfoController::class, 'changePre']); // 变更上级
            Route::get('team', [ProfileInfoController::class, 'team']); // 团队架构
            Route::get('options', [ProfileInfoController::class, 'options']); // 合伙人选项（前端）
            Route::post('mark-reviewed', [ProfileInfoController::class, 'markReviewStatus']); // 标记为已审核
            Route::get('payout', [ProfileInfoController::class, 'payout']); // 合伙人付款记录
            Route::get('customer', [ProfileInfoController::class, 'customer']); // 企业客户
        });

        // 三三制
        Route::group(['prefix' => 'teamProfile'], function () {
            Route::get('index', [TeamProfileController::class, 'index']); // 获取三三制列表
            Route::post('create', [TeamProfileController::class, 'store']); // 添加三三制总
            Route::get('edit/{id}', [TeamProfileController::class, 'edit']); // 查看三三制详情
            Route::post('edit/{id}', [TeamProfileController::class, 'update']); // 修改三三制
            Route::delete('delete/{id}', [TeamProfileController::class, 'destroy']); // 删除三三制
            Route::get('rank-options', [TeamProfileController::class, 'rankOptions']); // 三三制职级
        });

        // 客户
        Route::group(['prefix' => 'companys'], function () {
            Route::get('index', [CompanyController::class, 'index']); // 客户列表
            Route::get('edit/{id}', [CompanyController::class, 'edit']); // 客户详情
            Route::post('edit/{id}', [CompanyController::class, 'update']); // 更新客户信息
            Route::post('check', [CompanyController::class, 'check']); // 客户审核
            Route::get('reports', [CompanyController::class, 'reports']); // 尽调报告
        });


        // 回酬
        Route::group(['prefix' => 'bills'], function () {
            Route::get('index', [BillController::class, 'index']); // 付款记录列表
            Route::post('create', [BillController::class, 'store']); // 上传服务费单据
            Route::get('edit/{id}', [BillController::class, 'edit']); // 服务费单据详情
            Route::post('edit/{id}', [BillController::class, 'update']); // 编辑服务单据
            Route::delete('delete/{id}', [BillController::class, 'destroy']); // 删除
        });

        // 通知内容
        Route::group(['prefix' => 'notifications'], function () {
            Route::get('index', [NotificationController::class, 'index']); // 通知列表
            Route::post('create', [NotificationController::class, 'store']); // 发通知
            Route::get('edit/{id}', [NotificationController::class, 'edit']); // 查看通知详情
            Route::post('edit/{id}', [NotificationController::class, 'update']); // 编辑通知
        });

        // 资料
        Route::group(['prefix' => 'documents'], function () {
            Route::get('index', [DocumentInfoController::class, 'index']); // 资料列表
            Route::post('create', [DocumentInfoController::class, 'store']); // 上传资料
            Route::get('edit/{id}', [DocumentInfoController::class, 'edit']); // 查看资料详情
            Route::post('edit/{id}', [DocumentInfoController::class, 'update']); // 编辑资料
            Route::delete('delete/{id}', [DocumentInfoController::class, 'destroy']); // 删除
            Route::post('showFile/{id}', [DocumentInfoController::class, 'showFile']); // 显示文件

            Route::post('generateKey', [DocumentInfoController::class, 'generateDocumentToken']); //获取资料文件验证
            Route::get('generateKey/{id}', [DocumentInfoController::class, 'generateDocumentTokenByID']); //获取资料文件验证
            Route::withoutMiddleware('admin.auth')->group(function () {
                Route::get('preview/{token}', [DocumentInfoController::class, 'previewDocument']); //预览文件
                Route::get('download/{token}', [DocumentInfoController::class, 'downloadDocument']); //下载文件
            });

            Route::group(['prefix' => 'folders'], function () {
                Route::get('index', [DocumentFoldersController::class, 'index']); // 获取所有文件夹
                Route::get('files/{id}', [DocumentFoldersController::class, 'files']); // 获取文件夹下文件
                Route::post('create', [DocumentFoldersController::class, 'store']); // 创建文件夹
                Route::get('edit/{id}', [DocumentFoldersController::class, 'edit']); // 查看文件夹
                Route::post('edit/{id}', [DocumentFoldersController::class, 'update']); // 编辑文件夹
                Route::delete('delete/{id}', [DocumentFoldersController::class, 'destroy']); // 删除文件夹
            });
        });

        // 账户
        Route::group(['prefix' => 'account'], function () {
            Route::get('index/{type}', [ProfileInfoController::class, 'accountList']); // 账户列表
            Route::post('create', [ProfileInfoController::class, 'createAccount']); // 创建账户
            Route::get('edit/{id}', [ProfileInfoController::class, 'editAccount']); // 查看账户详情
            Route::post('edit/{id}', [ProfileInfoController::class, 'updateAccount']); // 编辑账户信息
            Route::delete('delete/{id}', [ProfileInfoController::class, 'destroyAccount']); // 删除
            Route::get('department', [ProfileInfoController::class, 'department']); // 部门列表
            Route::post('updatePassword', [ProfileInfoController::class, 'updatePassword']); // 修改用户密码
        });

        // 福利设置
        Route::group(['prefix' => 'benefitInfo'], function () {
            Route::get('index', [BenefitInfoController::class, 'index']); // 福利列表
            Route::post('create', [BenefitInfoController::class, 'store']); // 创建福利
            Route::get('edit/{id}', [BenefitInfoController::class, 'edit']); // 查看福利
            Route::post('edit/{id}', [BenefitInfoController::class, 'update']); // 编辑福利
            Route::delete('delete/{id}', [BenefitInfoController::class, 'destroy']); // 删除
        });

        // 合伙人申请福利
        Route::group(['prefix' => 'benefitUser'], function () {
            Route::get('index', [BenefitUserController::class, 'index']); // 申请列表
            Route::post('check', [BenefitUserController::class, 'check']); // 审核合伙人申请的福利
        });

        // 设置
        Route::group(['prefix' => 'setting'], function () {
            Route::get('index', [SettingController::class, 'index']); // 获取设置信息
            Route::post('setLangArea', [SettingController::class, 'setLangArea']); //设置语言地区
            Route::post('setNotify', [SettingController::class, 'setNotify']); // 信息与通知设置
            Route::get('modes', [SettingController::class, 'modes']); // 通知方式
            Route::post('password', [SettingController::class, 'password']); //修改密码
        });

        // 权限管理
        Route::group(['prefix' => 'power'], function () {
            Route::get('index', [PowerInfoController::class, 'index']); // 权限设置信息
            Route::post('savePower', [PowerInfoController::class, 'savePower']); // 保存权限设置
            Route::get('userPower', [PowerInfoController::class, 'userPower']); // 获取用户权限
        });

        // 活动日志
        Route::group(['prefix' => 'activeLog'], function () {
            Route::get('index', [ActiveLogController::class, 'index']); // 权限设置信息
        });

        // 活动足迹
        Route::group(['prefix' => 'loginLog'], function () {
            Route::get('index', [LoginLogController::class, 'list']); //登入足迹列表
            Route::post('quit', [LoginLogController::class, 'quit']); //登入足迹退出
        });

        // 电邮模板
        Route::group(['prefix' => 'emailTemplate'], function () {
            Route::get('index', [EmailTemplateController::class, 'index']); // 电邮模板列表
            Route::get('edit/{id}', [EmailTemplateController::class, 'edit']); // 查看电邮模板
            Route::post('edit', [EmailTemplateController::class, 'update']); // 编辑电邮
        });

        // 网页结构翻译
        Route::group(['prefix' => 'web'], function () {
            Route::get('structures', [StructureController::class, 'index']);
            Route::post('structures', [StructureController::class, 'store']);
            Route::post('structures/{id}', [StructureController::class, 'update']);
        });

        /*----联号事务所----*/
        Route::group(['prefix' => 'firm'], function () {
            Route::get('index', [FirmController::class, 'index']); //资料申请
            Route::post('check', [FirmController::class, 'check']); //申请资料
            Route::get('files', [FirmController::class, 'fileResource']); // doc.corporate-advisory 上的文件预览
        });

        // 财务管理
        Route::group(['prefix' => 'payment'], function () {
            Route::get('joinDetail', [PaymentController::class, 'joinDetail']); // 合伙人加盟费应收明细表
            Route::get('joinTotal', [PaymentController::class, 'joinTotal']); // 合伙人加盟费应收汇总表
            Route::post('upload/joinInvoice', [PaymentController::class, 'uploadJoinInvoice']); //上传发票
            Route::get('out', [PaymentController::class, 'out']); // 合伙人应付明细表
            Route::get('totalOut', [PaymentController::class, 'totalOut']); // 合伙人月结应付汇总表
            Route::get('refund', [PaymentController::class, 'refund']); // 退款列表
            Route::post('checkRefund', [PaymentController::class, 'checkRefund']);//确认退款
        });

        /*----发票管理----*/
        Route::group(['prefix' => 'invoice'], function () {
            Route::get('index', [InvoiceController::class, 'index']); // 开票信息
            Route::get('apply', [InvoiceDetailController::class, 'apply']); // 发票申请
        });

        // 数字人
        Route::group(['prefix' => 'digital-humans'], function () {
            Route::get('/', [DigitalHumanController::class, 'index']); // 数字人列表
            Route::post('download-file', [DigitalHumanController::class, 'download']); // 下载上传的数字人文件
            Route::post('check', [DigitalHumanController::class, 'check']); //审核数字人
            Route::get('zip', [DigitalHumanController::class, 'zipFile']); // 资料打包
        });

        // 绿智地球
        Route::group(['prefix' => 'gsp'], function () {
            Route::get('index', [GspController::class, 'index']); // 绿智地球入驻申请列表
            Route::post('generateGspFileToken', [GspController::class, 'generateGspFileToken']); //获取用户上传文件验证
            Route::get('previewFile/{token}', [GspController::class, 'previewFile']); //预览文件
            Route::get('downloadFile/{token}', [GspController::class, 'downloadFile']); //下载文件
            Route::post('checkForm', [GspController::class, 'checkForm']); //审核预审表格
            Route::post('checkContract', [GspController::class, 'checkContract']); //审核合同
            Route::get('reportDetail', [GspController::class, 'reportDetail']); //查看尽调材料详情
            Route::post('checkReportFile', [GspController::class, 'checkReportFile']); //审核尽调材料文件
            Route::post('checkReport', [GspController::class, 'checkReport']); //审核尽调材料
            Route::post('caCheckReport', [GspController::class, 'caCheckReport']); //审核尽调报告
            Route::post('generateResport', [GspController::class, 'generateResport']); //尽调报告合成
            Route::get('zip', [GspController::class, 'zipFile']); // 资料打包
            Route::post('download-file', [GspController::class, 'download']); // 下载尽调报告
            Route::get('select-options', [GspController::class, 'selectOptions']); // 选择项
            Route::get('progress', [GspController::class, 'progress']); // 当前进度状态
        });

        // 处理行政后台的文件
        Route::group(['prefix' => 'file'], function () {
            Route::get('resource', [FileController::class, 'fileResource']);
            Route::post('download', [FileController::class, 'streamDownload']);
        });

        /*--- 贵宾来访登录---*/
        Route::group(['prefix' => 'visitorLogin'], function () {
            Route::group(['prefix' => 'apply'], function () {
                Route::get('index', [VisitorApplyController::class, 'index']); // 到访申请表
                Route::get('detail', [VisitorApplyController::class, 'detail']); // 查看到访申请表
                Route::post('check', [VisitorApplyController::class, 'check']); // 审核到访申请表
                Route::post('downloadCard', [VisitorApplyController::class, 'downloadCard']);//下载邀请卡
            });

            Route::group(['prefix' => 'signin'], function () {
                Route::get('index', [VisitorSigninController::class, 'index']); // 贵宾登记表
                Route::get('detail', [VisitorSigninController::class, 'detail']); // 贵宾登记详情表
                Route::post('downloadCard', [VisitorSigninController::class, 'downloadCard']);//下载邀请卡
            });

            Route::group(['prefix' => 'report'], function () {
                Route::get('index', [VisitorReportController::class, 'index']); // 报告列表
                Route::get('detail', [VisitorReportController::class, 'detail']); // 报告详情
                Route::get('report', [VisitorReportController::class, 'report']); // 查看会谈结果报告
            });
        });

        /*--- 活动报名---*/
        Route::group(['prefix' => 'activity'], function () {
            Route::get('index', [ActivityController::class, 'index']); // 活动列表
            Route::post('add', [ActivityController::class, 'add']); // 添加活动
            Route::post('edit', [ActivityController::class, 'edit']); // 编辑活动
            Route::post('del', [ActivityController::class, 'del']); // 删除活动
            Route::get('signup', [ActivitySignupController::class, 'signup']); // 报名信息
        });

        // AI 办公桌面试问题
        Route::group(['prefix' => 'desk-interview-questions'], function () {
            Route::get('/', [InterviewQuestionController::class, 'index']); // 面试题列表
            Route::get('modules', [InterviewQuestionController::class, 'modules']); // 获取问题模块
            Route::post('/', [InterviewQuestionController::class, 'store']); // 添加面试题
            Route::put('/', [InterviewQuestionController::class, 'update']); // 编辑面试题
            Route::delete('/', [InterviewQuestionController::class, 'delete']); // 删除面试题
            Route::group(['prefix' => 'next-questions'], function () {
                Route::get('/', [InterviewQuestionController::class, 'nextQuestions']); // 获取追问问题
                Route::post('/', [InterviewQuestionController::class, 'storeNextQuestions']); // 添加追问问题
                Route::put('/', [InterviewQuestionController::class, 'updateNextQuestions']); // 编辑追问问题
                Route::delete('/', [InterviewQuestionController::class, 'deleteNextQuestions']); // 删除追问问题
            });
        });
        // 题库管理
        Route::group(['prefix' => 'exam-questions'], function () {
            Route::get('/', [ExamQuestionsController::class, 'index']); // 题目列表
            Route::post('/save', [ExamQuestionsController::class, 'store']); // 添加题目
            Route::put('/save/{id}', [ExamQuestionsController::class, 'update']); // 修改题目
            Route::delete('/destroy/{id}', [ExamQuestionsController::class, 'destroy']); // 修改题目
        });

        /*--- AI工作站板块 ----*/
        Route::group(['prefix' => 'desk'], function () {
            //项目管理
            Route::group(['prefix' => 'project'], function () {
                Route::get('list', [\App\Http\Controllers\Admin\v1\Desk\ProjectCategoriesController::class, 'list']);//项目列表
            });
            //考试管理
            Route::group(['prefix' => 'exam'], function () {
                Route::get('list', [\App\Http\Controllers\Admin\v1\Desk\ExamTypeController::class, 'list']);//列表
                Route::post('add', [\App\Http\Controllers\Admin\v1\Desk\ExamTypeController::class, 'add']);//添加
                Route::put('edit', [\App\Http\Controllers\Admin\v1\Desk\ExamTypeController::class, 'edit']);//编辑
                Route::delete('delete', [\App\Http\Controllers\Admin\v1\Desk\ExamTypeController::class, 'delete']);//删除
                Route::get('detail/{id}', [\App\Http\Controllers\Admin\v1\Desk\ExamTypeController::class, 'info']);//详情
            });
            //项目课程管理
            Route::group(['prefix' => 'course'], function () {
                Route::get('list', [\App\Http\Controllers\Admin\v1\Desk\ProjectServiceCourseController::class, 'listIntro']);//项目课程列表
                Route::post('add', [\App\Http\Controllers\Admin\v1\Desk\ProjectServiceCourseController::class, 'addIntro']);//新增课程简介和考试大纲
                Route::get('get/{id}', [\App\Http\Controllers\Admin\v1\Desk\ProjectServiceCourseController::class, 'getIntro']);//获取课程简介和考试大纲
                Route::put('update', [\App\Http\Controllers\Admin\v1\Desk\ProjectServiceCourseController::class, 'updateIntro']);//更新课程简介和考试大纲
                Route::delete('delete', [\App\Http\Controllers\Admin\v1\Desk\ProjectServiceCourseController::class, 'deleteIntro']);//删除课程简介和考试大纲
                //课程内容管理
                Route::group(['prefix' => 'courseContent'], function () {
                    Route::post('add', [\App\Http\Controllers\Admin\v1\Desk\ProjectServiceCourseController::class, 'add']);//新增
                    Route::get('get/{id}', [\App\Http\Controllers\Admin\v1\Desk\ProjectServiceCourseController::class, 'get']);//获取
                    Route::post('update', [\App\Http\Controllers\Admin\v1\Desk\ProjectServiceCourseController::class, 'update']);//更新
                    Route::delete('delete', [\App\Http\Controllers\Admin\v1\Desk\ProjectServiceCourseController::class, 'delete']);//删除
                    Route::get('list', [\App\Http\Controllers\Admin\v1\Desk\ProjectServiceCourseController::class, 'list']);//列表
                });
            });
        });
    });
});
