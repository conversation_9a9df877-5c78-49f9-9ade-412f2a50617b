<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CommissionSetModel extends Model
{
    protected $table = 'cna_commission_set';
    protected $guarded = [];
    protected $primaryKey = 'ID';

    // 角色
    const ROLE_TYPE_UNION = 1;  // 联盟合伙人
    const ROLE_TYPE_MANAGE = 2; // 管理合伙人
    const ROLE_TYPE_THREE  = 3; // 三三制
    const ROLE_TYPE_FIRMS  = 4; // 地区联号事务所
    const ROLES = [
        1 => '联盟合伙人',
        2 => '管理合伙人',
        3 => '三三制',
        4 => '地区联号事务所'
    ];
    // 服务项目
    const CASES = [
         'A' => 'A',
         'B' => 'B',
         'C' => 'C',
         'D' => 'D',
         'E' => 'E',
         'E2' => 'E*2',
    ];


}
