<?php

namespace App\Http\Controllers\Admin\v1\Desk;

use App\Models\Desk\ProjectServiceCourse;
use App\Models\Desk\ProjectServiceCourseIntro;
use App\Services\Desk\CourseService;
use Illuminate\Http\Request;

class ProjectServiceCourseController
{
    /**
     * describe：新增课程
     * add
     * @param Request $request
     * @param CourseService $courseService
     * @return null
     * 2025/6/13 - Mark
     */
    public function add(Request $request, CourseService $courseService)
    {
        $data = $request->validate([
            'service_data_id' => 'required|exists:mysql_ai_desk.cna_project_service_data,id',
            'name' => 'required',
            'type' => 'required|in:1,2',
            'file' => 'required|file|max:10240',
            'sort' => 'nullable',
            'total' => 'required|integer'
        ]);
        $info = $courseService->add($data);
        return $info;
    }

    /**
     * describe：获取课程
     * get
     * @param $id
     * @return null
     * 2025/6/13 - Mark
     */
    public function get($id)
    {
        $info = (new ProjectServiceCourse())->with('serviceData')->where('id', $id)->first();
        return responseSuccess($info, '查询成功');
    }

    /**
     * describe：更新课程
     * update
     * @param Request $request
     * @param CourseService $courseService
     * @return null
     * 2025/6/13 - Mark
     */
    public function update(Request $request, CourseService $courseService)
    {
        $data = $request->validate([
            'id' => 'required|exists:mysql_ai_desk.cna_project_service_course,id',
            'name' => 'required',
            'type' => 'required|in:1,2',
            'file' => 'file|max:10240',
            'service_data_id' => 'required|exists:mysql_ai_desk.cna_project_service_data,id',
            'sort' => 'nullable',
            'total' => 'required|integer'
        ]);
        $info = $courseService->update($data);
        return $info;
    }

    /**
     * describe：删除课程
     * delete
     * @param Request $request
     * @param CourseService $courseService
     * @return null
     * 2025/6/13 - Mark
     */
    public function delete(Request $request, CourseService $courseService)
    {
        $data = $request->validate([
            'id' => 'required|exists:mysql_ai_desk.cna_project_service_course,id',
        ]);
        $info = $courseService->delete($data);
    }

    /**
     * describe：课程列表
     * list
     * @param Request $request
     * @param CourseService $courseService
     * @return null
     * 2025/6/13 - Mark
     */
    public function list(Request $request, CourseService $courseService)
    {
        $list = (new ProjectServiceCourse())->with('serviceData')
            ->when($request->keyword, function ($query) use ($request) {
                $query->where('name', 'like', '%' . $request->keyword . '%');
            })
            ->orderBy('created_at', 'desc')
            ->paginate($request->PageSize ?? 10);

        $paginate = [
            'page_size' => $request->PageSize ?? 10,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];
        return responseSuccess(compact('list', 'paginate'));
    }

    /**
     * describe：新增课程介绍和考试大纲
     * addIntro
     * @param Request $request
     * @param CourseService $courseService
     * @return null
     * 2025/6/13 - Mark
     */
    public function addIntro(Request $request, CourseService $courseService)
    {
        $data = $request->validate([
            'service_data_id' => 'required|exists:mysql_ai_desk.cna_project_service_data,id',
            'outline' => 'required|file|max:10240',
            'intro' => 'required',
        ]);

        $info = $courseService->addIntro($data);
        return $info;
    }

    /**
     * describe：获取课程介绍和考试大纲
     * getIntro
     * @param $id
     * @return null
     * 2025/6/13 - Mark
     */
    public function getIntro($id)
    {
        $info = ProjectServiceCourseIntro::where('id', $id)->with('serviceData')->first();
        return responseSuccess($info, '查询成功');
    }

    /**
     * describe：更新课程介绍和考试大纲
     * updateIntro
     * @param Request $request
     * @param CourseService $courseService
     * @return null
     * 2025/6/13 - Mark
     */
    public function updateIntro(Request $request, CourseService $courseService)
    {
        $data = $request->validate([
            'id' => 'required|exists:mysql_ai_desk.cna_project_service_course_intro,id',
            'outline' => 'file|max:10240',
            'intro' => 'required',
            'service_data_id' => 'required|exists:mysql_ai_desk.cna_project_service_data,id',
        ]);

        $info = $courseService->updateIntro($data);
        return $info;
    }

    /**
     * describe：删除课程介绍和考试大纲
     * deleteIntro
     * @param Request $request
     * @param CourseService $courseService
     * @return null
     * 2025/6/13 - Mark
     */
    public function deleteIntro(Request $request, CourseService $courseService)
    {
        $data = $request->validate([
            'service_data_id' => 'required|exists:mysql_ai_desk.cna_project_service_data,id',
        ]);

        $info = $courseService->deleteIntro($data);
        return $info;
    }

    /**
     * describe：课程介绍和考试大纲列表
     * addContent
     * @param Request $request
     * @param CourseService $courseService
     * @return null
     * 2025/6/13 - Mark
     */
    public function listIntro(Request $request, CourseService $courseService)
    {
        $list = (new ProjectServiceCourseIntro())->with('serviceData')
            ->when($request->keyword, function ($query) use ($request) {
                $query->whereHas('serviceData', function ($query) use ($request) {
                    $query->where('title_zh', 'like', '%' . $request->keyword . '%');
                });
            })
            ->orderBy('created_at', 'desc')
            ->paginate($request->PageSize ?? 10);

        $paginate = [
            'page_size' => $request->PageSize ?? 10,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];
        return responseSuccess(compact('list', 'paginate'));
    }
}