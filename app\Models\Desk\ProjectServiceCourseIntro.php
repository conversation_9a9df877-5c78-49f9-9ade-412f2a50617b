<?php

namespace App\Models\Desk;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProjectServiceCourseIntro extends Model
{
    use SoftDeletes,HasFactory;
    protected $connection = 'mysql_ai_desk';
    protected $table = 'cna_project_service_course_intro';

    protected $fillable = [
        'outline',
        'intro',
        'service_data_id',
    ];

    //关联业务项目
    public function serviceData()
    {
        return $this->belongsTo(ProjectServiceData::class,'service_data_id','id')->select('id','title_zh');
    }

    protected function serializeDate(\DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s'); // 自定义时间格式
    }
}