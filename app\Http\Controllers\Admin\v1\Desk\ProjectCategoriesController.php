<?php

namespace App\Http\Controllers\Admin\v1\Desk;

use App\Models\Desk\ProjectCategories;
use Illuminate\Http\Request;

class ProjectCategoriesController
{
    /**
     * describe：查询项目列表
     * list
     * @param Request $request
     * @return null
     * 2025/6/13 - Mark
     */
    public function list(Request $request){
        $pageSize = $request->get('page_size', 10);
        $list = (new ProjectCategories())
            ->with('projectServiceData')
            ->paginate($pageSize);
        $paginate = [
            'page_size' => $pageSize,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];
        return responseSuccess(compact('list', 'paginate'));
    }
}