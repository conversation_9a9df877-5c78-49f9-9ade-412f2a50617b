<?php
namespace App\Http\Controllers\Admin\v1;

use App\Console\Commands\ProfileSettingNotify;
use App\Http\Controllers\Controller;
use App\Models\ActiveLog;
use App\Models\CommissionSetModel;
use App\Models\TeamProfile;
use App\Models\TeamRank;
use App\Services\PartnerIncomeService;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Request;

class TeamProfileController extends Controller
{
    /**
     * 三三制列表信息
     * @param Request $request
     * @return null
     */
    public function index(Request $request)
    {
        $pageSize = $request->get('page_size', 10);
        $keyword = $request->get('keyword');
        $user = $request->attributes->get('user');

        // 查询
        $list = TeamProfile::query()->when($keyword != '', function ($query) use ($keyword) {
            $query->orWhere('name', 'like', '%' . $keyword . '%')
                ->orWhere('phone', 'like', '%' . $keyword . '%')
                ->orWhere('email', 'like', '%' . $keyword . '%');
        })->orderBy('created_at', 'desc')->paginate($pageSize);


        $items = $list->items();

        $paginate = [
            'page_size' => $pageSize,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate'));
    }

    /**
     * 添加三三制总
     * @param Request $request
     * @return void
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required',
            'email' => 'required',
            'phone' => 'required',
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        $user = $request->attributes->get('user');
        $param = $request->all();

        // 新增
        $result = TeamProfile::query()->create([
            'name'      => $param['name'],
            'email'     => $param['email'],
            'phone'     => $param['phone'],
            'rank'      => 11,
            'created_at'=> date('Y-m-d H:i:s')
        ]);

        if ($result) {
            ActiveLog::log($user['profileID'], ActiveLog::ADMIN_API_V1_CREATE, ActiveLog::ADMIN_API_V1_TEAM,
                $result->id,$param['name'], ActiveLog::SYSTEM_CNA_ADMIN);

            return responseSuccess();
        } else {
            return responseFail(__('save failed'));
        }
    }

    /**
     * 查看三三制详情
     * @param Request $request
     * @param $id
     * @return void
     */
    public function edit(Request $request, $id)
    {
        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id']));// 参数缺失:id
        }

        // 获取详情
        $info = TeamProfile::where('id', $id)->first();
        if (empty($info)) {
            return responseFail(__('info no exist'));// 信息不存在
        }

        return responseSuccess($info);
    }

    /**
     * 编辑三三制信息
     * @param Request $request
     * @param $id
     * @return void
     */
    public function update(Request $request, $id)
    {
        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id']));// 参数缺失:id
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required',
            'email' => 'required',
            'phone' => 'required',
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        // 获取详情
        $info = TeamProfile::where('id', $id)->first();
        if (empty($info)) {
            return responseFail(__('info no exist'));// 信息不存在
        }

        $param = $request->all();
        $user = $request->attributes->get('user');

        $result = $info->update([
            'name'      => $param['name'],
            'email'     => $param['email'],
            'phone'     => $param['phone'],
            'updated_at'=> date('Y-m-d H:i:s')
        ]);

        if ($result) {
            ActiveLog::log($user['profileID'], ActiveLog::ADMIN_API_V1_EDIT, ActiveLog::ADMIN_API_V1_TEAM,
                $id,$param['name'], ActiveLog::SYSTEM_CNA_ADMIN);

            return responseSuccess();
        } else {
            return responseFail(__('save failed'));
        }
    }

    public function rankOptions()
    {
        return responseSuccess(TeamRank::rankOptions());
    }

    public function countFee(Request $request)
    {
        // 初始化计算比例
        $initData = $this->initCountFee();
        // 所有角色
        $roleData = CommissionSetModel::ROLES;
        // 三三制等级
        $roleRank = CommissionSetModel::where('type', CommissionSetModel::ROLE_TYPE_THREE)->orderBy('rank', 'desc')->pluck('remark', 'rank')->toArray();

        // 地区联号事务所等级
        $firmsRank = CommissionSetModel::where('type', CommissionSetModel::ROLE_TYPE_FIRMS)->orderBy('rank', 'asc')->pluck('remark', 'rank')->toArray();
        // 服务项目
        $cases = CommissionSetModel::CASES;
        if ($request->isMethod('post')) {

            // 处理请求参数
            $param = $request->all();
            if ($param['amount'] <= 0) {
                return view('admin.countFee',['param'=>$param]);
            }

            $rank = $param['rank'];
            if ($param['roleid'] == CommissionSetModel::ROLE_TYPE_FIRMS) {
                $rank = $param['rank1'];
            }

            // 计算管理津贴
            $result = PartnerIncomeService::countCommission($param['case'], $param['amount'], $param['roleid'], $rank);
            $data = $result[0];
            $teamData = $result[1];
            if ($data) {
                foreach ($data as $key => $val) {
                    if ($param['roleid'] == CommissionSetModel::ROLE_TYPE_UNION) {
                        if ($key == CommissionSetModel::ROLE_TYPE_UNION) {
                            $name = '联盟合伙人';
                        } else if ($key == CommissionSetModel::ROLE_TYPE_MANAGE) {
                            $name = '管理合伙人 (若有管理合伙人)';
                        }
                    } else  if ($param['roleid'] == CommissionSetModel::ROLE_TYPE_MANAGE) {
                        if ($key == 1) {
                            $name = '管理合伙人';
                        } else if ($key == 2) {
                            $name = '管理合伙人';
                        }
                    } else if ($param['roleid'] == CommissionSetModel::ROLE_TYPE_THREE) {
                        $name = $roleRank[$key];
                    } else if ($param['roleid'] == CommissionSetModel::ROLE_TYPE_FIRMS) {
                        $name = $firmsRank[$key];
                    }

                    $val['name'] = $name;
                    $val['value'] = number_format($val['value'], 2);
                    $data[$key] = $val;
                }
            }

            $param['case_name'] = $cases[$param['case']];
            $param['amount_name'] = number_format($param['amount'], 2);
            return view('admin.countFee', [
                'data'          => $data,
                'teamData'      => $teamData,
                'param'         => $param,
                'data_init'     => $initData[0],
                'teamData_init' => $initData[1],
                'firmsData_init'=> $initData[2],
                'roleData'      => $roleData,
                'roleRank'      => $roleRank,
                'firmsRank'     => $firmsRank,
                'cases'         => $cases,
            ]);
        } else {
            // 处理其他类型的请求
            return view('admin.countFee', [
                'data_init'      => $initData[0],
                'teamData_init'  => $initData[1],
                'firmsData_init' => $initData[2],
                'roleData'       => $roleData,
                'roleRank'       => $roleRank,
                'firmsRank'      => $firmsRank,
                'cases'          => $cases,
            ]);
        }

    }

    /**
     * 初始化百分比显示
     * @return void
     */
    public function initCountFee()
    {

        $arrData = CommissionSetModel::get()->toArray();
        $teamData_init = [];// 联盟合伙人和管理合伙人
        $data_init = []; // 三三制
        $firms_init = []; // 地区联号事务所
        if ($arrData) {
            foreach ($arrData as $key => $val) {
                if (in_array($val['type'], [ CommissionSetModel::ROLE_TYPE_UNION , CommissionSetModel::ROLE_TYPE_MANAGE])) {
                    $teamData_init[$val['rank']] = $val;
                } else if ($val['type'] == CommissionSetModel::ROLE_TYPE_THREE) { // 三三制
                    $data_init[$val['rank']] = $val;
                } else if ($val['type'] == CommissionSetModel::ROLE_TYPE_FIRMS) { // 联号事务所
                    $firms_init[$val['rank']] = $val;
                }

            }

            return [$data_init, $teamData_init, $firms_init];
        }


    }

    /**
     * 个人所得税计算
     * @return void
     */
    public function countTax(Request $request)
    {
        $tax = 0;
        $oneTax = 0;
        $twoTax = 0;
        $type = 1;
        $param = $request->all();
        $amount1 = isset($param['amount1'])?$param['amount1']:0;
        $amount2 = isset($param['amount2'])?$param['amount2']:0;
        $amount3 = isset($param['amount3'])?$param['amount3']:0;

        if (empty($amount2) && empty($amount3)) {
            $tax = PartnerIncomeService::taxCalculate($amount1);
            $type = 1;
        } else if ($amount1 && $amount2 && empty($amount3)) {
            $type = 2;
            $sum = bcadd($amount1, $amount2, 2);
            $tax = PartnerIncomeService::taxCalculate($sum);
            // 第一次扣的
            $oneTax = PartnerIncomeService::taxCalculate($amount1);
            $tax = bcsub($tax, $oneTax, 2);
        } else if ($amount1 && $amount2 && $amount3) {
            $type = 3;
            $sum = bcadd($amount1, $amount2, 2);
            $sum = bcadd($sum, $amount3, 2);
            $tax = PartnerIncomeService::taxCalculate($sum);

            // 第一次扣的
            $oneTax = PartnerIncomeService::taxCalculate($amount1);

            // 第二次扣的
            $twoAmount = bcadd($amount1, $amount2, 2);
            $twoTax = PartnerIncomeService::taxCalculate($twoAmount);
            $twoTax = bcsub($twoTax, $oneTax, 2);

            $tax = bcsub($tax, $oneTax, 2);
            $tax = bcsub($tax, $twoTax, 2);
        }


        return view('admin.clountTax', compact('tax','oneTax','twoTax', 'amount1', 'amount2', 'amount3', 'type'));

    }


    /**
     * 平台所得税计算
     * @return void
     */
    public function countPlat(Request $request)
    {
        $tax = 0;
        $oneTax = 0;
        $twoTax = 0;
        $type = 1;
        $param = $request->all();
        $amount1 = isset($param['amount1'])?$param['amount1']:0;
        $amount2 = isset($param['amount2'])?$param['amount2']:0;
        $amount3 = isset($param['amount3'])?$param['amount3']:0;

        if (empty($amount2) && empty($amount3)) {
            $tax = PartnerIncomeService::taxCalculate($amount1);
            $type = 1;
        } else if ($amount1 && $amount2 && empty($amount3)) {
            $type = 2;
            $sum = bcadd($amount1, $amount2, 2);
            $tax = PartnerIncomeService::taxCalculate($sum);
            // 第一次扣的
            $oneTax = PartnerIncomeService::taxCalculate($amount1);
            $tax = bcsub($tax, $oneTax, 2);
        } else if ($amount1 && $amount2 && $amount3) {
            $type = 3;
            $sum = bcadd($amount1, $amount2, 2);
            $sum = bcadd($sum, $amount3, 2);
            $tax = PartnerIncomeService::taxCalculate($sum);

            // 第一次扣的
            $oneTax = PartnerIncomeService::taxCalculate($amount1);

            // 第二次扣的
            $twoAmount = bcadd($amount1, $amount2, 2);
            $twoTax = PartnerIncomeService::taxCalculate($twoAmount);
            $twoTax = bcsub($twoTax, $oneTax, 2);

            $tax = bcsub($tax, $oneTax, 2);
            $tax = bcsub($tax, $twoTax, 2);
        }


        return view('admin.countPlat', compact('tax','oneTax','twoTax', 'amount1', 'amount2', 'amount3', 'type'));

    }




}