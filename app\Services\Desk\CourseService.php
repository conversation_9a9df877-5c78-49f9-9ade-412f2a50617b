<?php

namespace App\Services\Desk;

use App\Models\Desk\ProjectServiceCourse;
use App\Models\Desk\ProjectServiceCourseIntro;
use App\Services\OssService;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class CourseService
{
    /**
     * describe：新增课程
     * add
     * @param $data
     * @return null
     * 2025/6/13 - Mark
     */
    public function add($data){
        try {
            //判断当前业务是否已经有了课程和考试大纲
            $model = ProjectServiceCourse::where('name', $data['name'])->first();
            if ($model) {
                return responseFail('当前业务已有同名课程');
            }
            //获取文件
            $file = $data['file'];
            //上传文件 获取文件路径
            $filePath = OssService::upload($file);
            $data['url'] = $filePath;
            $model = new ProjectServiceCourse();
            $model->name = $data['name'];
            $model->type = $data['type'];
            $model->url = $data['url'];
            $model->sort = $data['sort'] ?? 0;
            $model->total = $data['total'];
            $model->service_data_id = $data['service_data_id'];
            $model->save();

        } catch (\Exception $e) {
            return responseFail($e->getMessage());
        }
        return responseSuccess($model, '新增成功');
    }

    /**
     * describe：更新课程
     * update
     * @param $data
     * @return null
     * 2025/6/13 - Mark
     */
    public function update($data){
        try {
            $model = ProjectServiceCourse::where('id', $data['id'])->first();
            if (!$model) {
                return responseFail('未找到课程');
            }
            //旧文件
            $oldFile = $model->url;
            //获取文件
            $file = $data['file'];
            //上传文件 获取文件路径
            $filePath = OssService::upload($file);
            $data['url'] = $filePath;
            $model->name = $data['name'];
            $model->type = $data['type'];
            $model->url = $data['url'];
            $model->sort = $data['sort'] ?? 0;
            $model->total = $data['total'];
            $model->service_data_id = $data['service_data_id'];
            $model->save();

        }catch (\Exception $e){
            return responseFail($e->getMessage());
        }
        return responseSuccess($model, '更新成功');
    }

    /**
     * describe：删除课程
     * delete
     * @param $data
     * @return void|null
     * 2025/6/13 - Mark
     */
    public function delete($data){
        try {
            $model = ProjectServiceCourse::where('id', $data['id'])->first();
            if (!$model) {
                return responseFail('没有找到课程');
            }
            $model->delete();
        }catch (\Exception $e){
            return responseFail($e->getMessage());
        }
        return responseSuccess($model, '删除成功');
    }


    /**
     * describe：新增课程相关的简介和考试大纲
     * addIntro
     * @param $data
     * @return null
     * 2025/6/13 - Mark
     */
    public function addIntro($data)
    {
        try {
            //判断当前业务是否已经有了课程和考试大纲
            $model = ProjectServiceCourseIntro::where('service_data_id', $data['service_data_id'])->first();
            if ($model) {
                return responseFail('当前业务已经添加过课程和考试大纲');
            }
            //获取文件
            $file = $data['outline'];
            //上传文件 获取文件路径
            $filePath = OssService::upload($file);

            $data['outline'] = $filePath;
            $model = new ProjectServiceCourseIntro();
            $model->intro = $data['intro'];
            $model->outline = $data['outline'];
            $model->service_data_id = $data['service_data_id'];
            $model->save();

        } catch (\Exception $e) {
            return responseFail($e->getMessage());
        }
        return responseSuccess($model, '新增成功');
    }


    /**
     * describe：更新课程相关的简介和考试大纲
     * updateIntro
     * @param $data
     * @return null
     * 2025/6/13 - Mark
     */
    public function updateIntro($data)
    {
        try {
            $model = ProjectServiceCourseIntro::where('id', $data['id'])->first();
            if (!$model) {
                return responseFail('当前业务没有课程和考试大纲');
            }
            //旧文件
            $oldFile = $model->outline;
            //获取文件
            $file = $data['outline'];
            //上传文件 获取文件路径
            $filePath = OssService::upload($file);
            $data['outline'] = $filePath;

            $model->intro = $data['intro'];
            $model->outline = $data['outline'];
            $model->service_data_id = $data['service_data_id'];
            $model->save();

        }catch (\Exception $e){
            return responseFail($e->getMessage());
        }
        return responseSuccess($model, '更新成功');
    }

    /**
     * describe：删除课程相关的简介和考试大纲
     * deleteIntro
     * @param $data
     * @return null
     * 2025/6/13 - Mark
     */
    public function deleteIntro($data)
    {
        try {
            $model = ProjectServiceCourseIntro::where('service_data_id', $data['service_data_id'])->first();
            if (!$model) {
                return responseFail('当前业务没有课程和考试大纲');
            }
            $model->outline;
            $model->delete();

        }catch (\Exception $e){
            return responseFail($e->getMessage());
        }
        return responseSuccess($model, '删除成功');
    }

}