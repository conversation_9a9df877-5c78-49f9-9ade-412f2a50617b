<?php

namespace App\Services;

use App\Exceptions\DefaultException;
use App\Models\CompanyCategoryModel;
use App\Models\CompanyGsp;
use App\Models\CountryModel;
use App\Models\GspOption;
use Fpdi\Fpdi;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class GspService
{
    public function getForm($data)
    {
        try {
            $originalContract = storage_path('pdf/gsp_form.pdf');
            $pdf = new \Fpdi\Fpdi();
            $pdf->AddPage();
            $pdf->setSourceFile($originalContract);
            $tplIdx = $pdf->importPage(1);
            $pdf->useTemplate($tplIdx);
            $pdf->AddGBFont();
            $pdf->SetFont('GB', '', 10);
            //填空
            $pdf->SetTextColor(0, 0, 0); // 黑色文本
            $pdf->SetXY(63, 37);
            $pdf->Write(10, $this->mbConvertEncoding($data['name']));
            $pdf->SetTextColor(0, 0, 0); // 黑色文本
            $pdf->SetXY(63, 44);
            $pdf->Write(10, $this->mbConvertEncoding($data['credit_code']));
            $pdf->SetTextColor(0, 0, 0); // 黑色文本
            $pdf->SetXY(63, 51);
            $pdf->Write(10, $this->mbConvertEncoding(number_format($data['register_capital'],2)));
            $pdf->SetTextColor(0, 0, 0); // 黑色文本
            $pdf->SetXY(151, 51);
            $pdf->Write(10, $this->mbConvertEncoding(number_format($data['paid_capital'], 2)));
            $options = GspOption::where('type', GspOption::TYPE_COMPANY_CATEGORY)->pluck('title', 'id');
            $ccIdsArr = explode(',', $data['company_category']);
            $ccString = [];
            foreach ($ccIdsArr as $id) {
                if (isset($options[$id])) {
                    $ccString[] = $options[$id];
                }
            }
            $ccString = implode(',', $ccString);
            $ccArray = $this->splitString($ccString, 37);
            for ($i = 0; $i < count($ccArray); $i++) {
                $postionY = 58 + $i*5;
                $pdf->SetTextColor(0, 0, 0); // 黑色文本
                $pdf->SetXY(63, $postionY);
                $pdf->Write(10, $this->mbConvertEncoding($ccArray[$i]));
            }
            $descArray = $this->splitString($data['desc'], 37);
            for ($i = 0; $i < count($descArray); $i++) {
                $postionY = 65 + $i*5;
                $pdf->SetTextColor(0, 0, 0); // 黑色文本
                $pdf->SetXY(63, $postionY);
                $pdf->Write(10, $this->mbConvertEncoding($descArray[$i]));
            }
            $mbArray = $this->splitString($data['main_business'], 37);
            for ($i = 0; $i < count($mbArray); $i++) {
                $postionY = 86 + $i*5;
                $pdf->SetTextColor(0, 0, 0); // 黑色文本
                $pdf->SetXY(63, $postionY);
                $pdf->Write(10, $this->mbConvertEncoding($mbArray[$i]));
            }
            $rbArray = $this->splitString($data['related_business'], 37);
            for ($i = 0; $i < count($rbArray); $i++) {
                $postionY = 100 + $i*5;
                $pdf->SetTextColor(0, 0, 0); // 黑色文本
                $pdf->SetXY(63, $postionY);
                $pdf->Write(10, $this->mbConvertEncoding($rbArray[$i]));
            }
            $customerArray = $this->splitString($data['customer'], 37);
            for ($i = 0; $i < count($customerArray); $i++) {
                $postionY = 114 + $i*5;
                $pdf->SetTextColor(0, 0, 0); // 黑色文本
                $pdf->SetXY(63, $postionY);
                $pdf->Write(10, $this->mbConvertEncoding($customerArray[$i]));
            }
            $pdf->SetTextColor(0, 0, 0); // 黑色文本
            $pdf->SetXY(63, 135);
            $pdf->Write(10, $this->mbConvertEncoding(number_format($data['annual_revenue'], 2)));
            $pdf->SetTextColor(0, 0, 0); // 黑色文本
            $pdf->SetXY(151, 135);
            $pdf->Write(10, $this->mbConvertEncoding(number_format($data['annual_cost'],2)));
            $pdf->SetTextColor(0, 0, 0); // 黑色文本
            $pdf->SetXY(63, 149);
            $pdf->Write(10, $this->mbConvertEncoding(number_format($data['total_assets'], 2)));
            $pdf->SetTextColor(0, 0, 0); // 黑色文本
            $pdf->SetXY(151, 149);
            $pdf->Write(10, $this->mbConvertEncoding(number_format($data['total_liability'], 2)));
            $pdf->SetTextColor(0, 0, 0); // 黑色文本
            $pdf->SetXY(63, 163);
            $pdf->Write(10, $this->mbConvertEncoding($data['contact_name']));
            $pdf->SetTextColor(0, 0, 0); // 黑色文本
            $pdf->SetXY(151, 163);
            $pdf->Write(10, $this->mbConvertEncoding($data['contact_position']));
            $pdf->SetTextColor(0, 0, 0); // 黑色文本
            $pdf->SetXY(63, 170);
            $pdf->Write(10, $this->mbConvertEncoding($data['phone']));
            $pdf->SetTextColor(0, 0, 0); // 黑色文本
            $pdf->SetXY(151, 170);
            $pdf->Write(10, $this->mbConvertEncoding($data['email']));

            $options = GspOption::where('type', GspOption::TYPE_ECONOMIC_BEHAVIOR)->pluck('title', 'id');
            $ebIdsArr = explode(',', $data['economic_behavior']);
            $ebString = [];
            foreach ($ebIdsArr as $id) {
                if (isset($options[$id])) {
                    $ebString[] = $options[$id];
                }
            }
            $ebString = implode(',', $ebString);
            $ebArray = $this->splitString($ebString, 37);
            for ($i = 0; $i < count($ebArray); $i++) {
                $postionY = 182 + $i*5;
                $pdf->SetTextColor(0, 0, 0); // 黑色文本
                $pdf->SetXY(63, $postionY);
                $pdf->Write(10, $this->mbConvertEncoding($ebArray[$i]));
            }
            $options = GspOption::where('type', GspOption::TYPE_INDUSTRY_GROUP)->pluck('title', 'id');
            $igIdsArr = explode(',', $data['industry_group']);
            $igString = [];
            foreach ($igIdsArr as $id) {
                if (isset($options[$id])) {
                    $igString[] = $options[$id];
                }
            }
            $igString = implode(',', $igString);
            $igArray = $this->splitString($igString, 37);
            for ($i = 0; $i < count($igArray); $i++) {
                $postionY = 196 + $i*5;
                $pdf->SetTextColor(0, 0, 0); // 黑色文本
                $pdf->SetXY(63, $postionY);
                $pdf->Write(10, $this->mbConvertEncoding($igArray[$i]));
            }
            $pdf->SetTextColor(0, 0, 0); // 黑色文本
            $pdf->SetXY(63, 210);
            $pdf->Write(10, $this->mbConvertEncoding(number_format($data['annual_trade'], 2)));

            if ($data['target_country']) {
                $data['target_country'] = CountryModel::whereIn('countryID', explode(',', $data['target_country']))->pluck('countryZH')->toArray();
                $data['target_country'] = implode(',',  $data['target_country']);
            }

            $pdf->SetTextColor(0, 0, 0); // 黑色文本
            $pdf->SetXY(151, 210);
            $pdf->Write(10, $this->mbConvertEncoding($data['target_country']));
            $qrcode = \SimpleSoftwareIO\QrCode\Facades\QrCode::format('png')->size(150)
                ->generate($data['link'] ?: 'https://ai.corporate-advisory.cn');
            $filenameQr = 'gsp/'.Str::random(16).'.png';

            // 检查目录是否存在，如果不存在则创建
            $directory = storage_path('app/public/gsp/');
            if (!is_dir($directory)) {
                mkdir($directory, 0777, true); // 递归创建目录
            }

            file_put_contents(storage_path('app/public/'.$filenameQr), $qrcode);
            $pdf->Image(storage_path('app/public/'.$filenameQr), 151, 240, 40, 40);

            $filename = 'gsp/'.time().Str::random(16).'.pdf';
            $file = storage_path('app/public/'.$filename);
            $pdf->Output('F', $file);
            if (isset($data['form']) && $data['form']) {
                @unlink(storage_path('app/public/'.$data['form']));
            }
            @unlink($filenameQr);
/*            CompanyGsp::where('id', $data['id'])->update([
                'form' => $filename,
            ]);*/
            return $filename;
        } catch (\Exception $e) {
            throw new DefaultException('生成表格失败:'.$e->getMessage().$e->getLine().$e->getFile());
        }
    }
    
    /**
     * Method mbConvertEncoding
     * 中文转编码
     *
     * @param $text $text [explicite description]
     *
     * @return void
     */
    private function mbConvertEncoding($text)
    {
        return mb_convert_encoding($text, 'GBK', 'UTF-8');
    }
    
    /**
     * Method splitString
     * 分割字符
     *
     * @param $str $str [explicite description]
     * @param $maxLength $maxLength [explicite description]
     *
     * @return void
     */
    private function splitString($str, $maxLength = 16) {
        // 初始化结果数组
        $result = [];
    
        // 循环分割字符串
        while (mb_strlen($str, 'UTF-8') > $maxLength) {
            // 截取前 $maxLength 长度的子字符串
            $part = mb_substr($str, 0, $maxLength, 'UTF-8');
            $result[] = str_replace(' ', '', $part);
            // 去掉已经分割的部分
            $str = mb_substr($str, $maxLength, null, 'UTF-8');
        }
    
        // 如果还有剩余的字符串，加入结果数组
        if (mb_strlen($str, 'UTF-8') > 0) {
            $result[] = str_ireplace(' ', '', $str);
        }
    
        return $result;
    }


}