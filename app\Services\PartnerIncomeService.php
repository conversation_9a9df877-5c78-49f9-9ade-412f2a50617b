<?php
namespace App\Services;
use App\Models\CommissionSetModel;
use App\Models\PaymentModel;
use App\Models\TeamRank;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

/**
 * 合伙人三三制收益计算(包括税费)
 */
class PartnerIncomeService
{
    // 收益类型
    const CASE_CATE  = [
        'A' => 'A-专业服务费',
        'B' => 'B-专项工作费',
        'C' => 'C-项目开户费',
        'D' => 'D-常规服务费',
        'E' => 'E-成功交易费'
    ];


    /**
     * 计算津贴
     * @param $case    // 服务类型: ('A','B', 'C', 'D', 'E', 'E *2')
     * @param $amount  // 金额
     * @param $roleid  // 角色：1联盟合伙人; 2管理合伙人; 3三三制; 4联号事务所;
     * @param $rank    // 当前等级 (如果是三三制,联号事务所 )
     * @return array   // [$arrData=>个人管理津贴, $teamData=>三三制团队津贴]
     */
    public static function countCommission($case, $amount, $roleid, $rank)
    {
        // 所有数据
        $arrData = [];
        // 三三制合伙人费用
        $teamData = [];

        // 查询合伙人KPI管理津贴比例(%)
        $associate = CommissionSetModel::query()->whereIn('type', [CommissionSetModel::ROLE_TYPE_UNION,CommissionSetModel::ROLE_TYPE_MANAGE])
            ->pluck('case'.$case, 'rank')
            ->toArray();

        // 查询三三制KPI管理津贴比(%)
        $teamFee = CommissionSetModel::query()->where('type', CommissionSetModel::ROLE_TYPE_THREE)
            ->pluck('case'.$case, 'rank')
            ->toArray();

        // 查询联号事务所KPI管理津贴比(%)
        $firmFee = CommissionSetModel::query()->where('type', CommissionSetModel::ROLE_TYPE_FIRMS)
            ->pluck('case'.$case, 'rank')
            ->toArray();

        // 角色：联盟合伙人或管理合伙人
        if ($roleid == CommissionSetModel::ROLE_TYPE_UNION || $roleid == CommissionSetModel::ROLE_TYPE_MANAGE) {

            $arrData['1'] = [ // 自已
                'key' => $associate[1],
                'value' => bcmul($amount, bcdiv($associate[1], 100,6), 2),
                'jintie' => '个人分成'
            ];
            $arrData['2'] = [// 上级管理合伙人
                'key' => $associate[2],
                'value' => bcmul($amount, bcdiv($associate[2], 100, 6), 2),
                'jintie' => '管理津贴'
            ];

        }  else if ($roleid == CommissionSetModel::ROLE_TYPE_THREE) {
            // 角色：三三制

            if ($rank > 0) {
                for ($i = $rank+1; $i <=10; $i++) { // 当前所有上级
                    $arrData[$i] = [
                        'key' => $teamFee[$i],
                        'value' => bcmul($amount, bcdiv($teamFee[$i], 100, 6), 2),
                        'jintie' => '运营津贴'
                    ];
                }

                // 个人分成
                $fee = bcmul($amount, bcdiv($associate[1], 100,6), 2);
                $rankName =  CommissionSetModel::where('type', CommissionSetModel::ROLE_TYPE_THREE)
                    ->where('rank', $rank)
                    ->value('remark');

                $teamData[1] = [
                    'key'   => $associate[1],
                    'value' => number_format($fee, 2),
                    'name'  => "$rankName",
                    'jintie' => '个人分成'
                ];

                // 上一级加上管理合伙人津贴
                if ($rank < 10) {
                    $parentFee = bcmul($amount, bcdiv($associate[2], 100, 6), 2) ;
                    $parentRankName = CommissionSetModel::where('type', CommissionSetModel::ROLE_TYPE_THREE)
                        ->where('rank', $rank+1)
                        ->value('remark');

                    $teamData[2] = [
                        'key' => $associate[2],
                        'value' => number_format($parentFee, 2),
                        'name' => "$parentRankName",
                        'jintie' => '管理津贴'
                    ];
                }


            }
        } else if ($roleid == CommissionSetModel::ROLE_TYPE_FIRMS) {
            // 角色：联号事务所

            if ($rank > 0) {
                for ($i = $rank-1; $i >=1; $i--) { // 当前所有上级
                    $arrData[$i] = [
                        'key' => $firmFee[$i],
                        'value' => bcmul($amount, bcdiv($firmFee[$i], 100, 6), 2),
                        'jintie' => '服务津贴'
                    ];
                }

                // 个人分成
                $fee = bcmul($amount, bcdiv($associate[1], 100,6), 2);
                $rankName =  CommissionSetModel::where('type', CommissionSetModel::ROLE_TYPE_THREE)
                    ->where('rank', $rank)
                    ->value('remark');

                $teamData[1] = [
                    'key'   => $associate[1],
                    'value' => number_format($fee, 2),
                    'name'  => "$rankName",
                    'jintie' => '个人分成'
                ];

                // 上一级加上管理合伙人津贴
                if ($rank < 10) {
                    $parentFee = bcmul($amount, bcdiv($associate[2], 100, 6), 2) ;
                    $parentRankName = CommissionSetModel::where('type', CommissionSetModel::ROLE_TYPE_THREE)
                        ->where('rank', $rank+1)
                        ->value('remark');

                    $teamData[2] = [
                        'key' => $associate[2],
                        'value' => number_format($parentFee, 2),
                        'name' => "$parentRankName",
                        'jintie' => '管理津贴'
                    ];
                }


            }
        }

        return [$arrData, $teamData];

    }

    /**
     * 按照金额档次计算个税
     * @param $amount
     * @return int|string
     */
    public static function taxCalculate($amount)
    {

        // 计算应纳税所得额
        $excluding_tax = config('tax.excluding_tax');
        $payable_config1 = config('tax.payable_config1');

        if ($amount <= $payable_config1['payable_sub_number']) { // 小于等于800不用交税
            return 0;
        }

        // 除以1.01换算成不含税收入
        $taxAmount = bcdiv($amount, $excluding_tax, 8);
        if ($amount <= $payable_config1['payable_point']) {
            $taxAmount = bcsub($taxAmount, $payable_config1['payable_sub_number'], 8);
        } else {
            $taxAmount = bcmul($taxAmount, $payable_config1['payable_mul_number'], 8);
        }

        // 计算减免费用
        $reduceFee = self::reduceCalculate($amount);
        // 减去减免费用
        $taxAmount =  bcsub($taxAmount, $reduceFee, 8);


        // 劳务报酬所得个人税计算
        $tax_brackets = config('tax.tax_brackets');
        $data = [];
        if ($tax_brackets) {
            foreach ($tax_brackets as $val) {
                if (($taxAmount > $val['min'] && $taxAmount <= $val['max'])
                    || ($taxAmount > $val['min'] && $val['max']==0 )) {
                    $data = $val;
                    break;
                }
            }
        }

        if (isset($data['rate'])) {
            $tax = bcmul( $taxAmount, $data['rate'], 8);
            $tax = bcsub($tax, $data['deduction'],8);
            return $tax;
        } else {
            return null;
        }


    }


    /**
     * 平台收入不同等级减免费用
     * @return int|string
     */
    public static function reduceCalculate($amount)
    {
        $payable_config2 = config('tax.payable_config2');
        $excluding_tax = config('tax.excluding_tax');

        $reduceFee = 0;
        if ($amount <= $payable_config2['payable_free']) {
            $reduceFee = 0;
        } else if ($amount <= $payable_config2['payable_sum_point']) {
            $reduceFee = bcdiv($amount, $excluding_tax, 8);
            $reduceFee = bcmul($reduceFee, $payable_config2['payable_tax_rate'], 8);
            $reduceFee = bcmul($reduceFee, $payable_config2['payable_less_number'], 8);
            $reduceFee = bcdiv($reduceFee, $payable_config2['payable_div_number'], 8);

        } else if ( $amount > $payable_config2['payable_sum_point']) {
            $reduceFee = bcdiv($amount, $excluding_tax, 8);
            $reduceFee = bcmul($reduceFee, $payable_config2['payable_tax_rate'], 8);
            $reduceFee = bcmul($reduceFee, $payable_config2['payable_greater_number'], 8);
            $reduceFee = bcdiv($reduceFee, $payable_config2['payable_div_number'], 8);

        }

        return $reduceFee;
    }

    /**
     * 计算本次应交的税费
     * @param $profileId 纳税人
     * @param $amount 本次收入金额
     * @return int|string
     */
    public static function calculateTax($profileId, $amount)
    {
        // 获取本月第一天和最后一天
        $firstDayOfMonth = Carbon::now()->startOfMonth();
        $lastDayOfMonth = Carbon::now()->endOfMonth();

        // 当月所有税前收入
        $allIncome = PaymentModel::where('profileID', $profileId)
            ->where('type', 1)
            ->whereBetween('createtime', [$firstDayOfMonth, $lastDayOfMonth])
            ->sum('divide_amount');

        $allIncome = bcadd($allIncome, $amount, 8);

        // 总的扣税
        $allTax = self::taxCalculate($allIncome);


        // 减去当月扣的税，等于当前应交税
        $tax = $allTax;
        $incomePayment = PaymentModel::where('profileID', $profileId)
            ->where('type', 1)
            ->whereBetween('createtime', [$firstDayOfMonth, $lastDayOfMonth])
            ->get();

        if ($incomePayment) {
            foreach ($incomePayment as $val) {

                $tax = bcsub($tax, $val->proxy_fax, 2);

            }
        }

        return $tax;

    }

}