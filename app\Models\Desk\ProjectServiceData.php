<?php

namespace App\Models\Desk;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProjectServiceData extends Model
{
    use SoftDeletes,HasFactory;
    protected $connection = 'mysql_ai_desk';
    protected $table = 'cna_project_service_data';

    protected $fillable = [
        'title_zh',
        'title_zt',
        'title_en',
        'title_ms',
        'currency',
        'price',
        'tip_zh',
        'tip_zt',
        'tip_en',
        'tip_ms',
        'case',
        'remark',
        'payment_main_id',
        'currency_id',
        'status',
        'code',
        'is_exempt',
    ];

    //关联简介
    public function intro(){
        return $this->hasOne(ProjectServiceCourseIntro::class,'service_data_id','id');
    }
    protected function serializeDate(\DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s'); // 自定义时间格式
    }
}