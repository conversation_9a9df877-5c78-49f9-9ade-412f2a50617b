{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.1", "ext-bcmath": "*", "ext-curl": "*", "ext-exif": "*", "ext-gd": "*", "alibabacloud/dm-20151123": "1.2.2", "alibabacloud/dysmsapi-20170525": "3.0.0", "alibabacloud/ocr-api-20210707": "3.1.2", "contao/imagine-svg": "*", "guzzlehttp/guzzle": "^7.2", "intervention/image": "^2.7", "jenssegers/agent": "*", "laravel/framework": "^10.10", "laravel/sanctum": "^3.3", "laravel/tinker": "^2.8", "mews/captcha": "^3.3", "meyfa/php-svg": "*", "phpoffice/phpword": "*", "predis/predis": "^2.0", "setasign/fpdf": "^1.8", "setasign/fpdi-tcpdf": "^2.3", "simplesoftwareio/simple-qrcode": "1.3.*", "tymon/jwt-auth": "^2.1", "wechatpay/wechatpay": "^1.4", "workerman/gateway-worker": "^v3.0.23||^4.0.0", "ext-imagick": "*"}, "require-dev": {"fakerphp/faker": "^1.9.1", "firebase/php-jwt": "^6.10", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Fpdi\\": "app/Services/Fpdi/"}, "files": ["app/Common/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force", "@php artisan lang:update"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}