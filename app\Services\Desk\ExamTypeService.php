<?php

namespace App\Services\Desk;

use App\Models\ExamTypeModel;

class ExamTypeService
{
    /**
     * describe：新增考试类型
     * add
     * @param $data
     * @return null
     * 2025/6/13 - Mark
     */
    public function add($data)
    {
        try {
            //判断是否已存在
            $isExist = (new ExamTypeModel())->where('project_id', $data['project_id'])->where('course_id', $data['course_id'])->first();
            if ($isExist) {
                return responseFail('该课程已存在考试');
            }
            $model = new ExamTypeModel();
            $model->name = $data['name'];
            $model->project_id = $data['project_id'];
            $model->course_id = $data['course_id'];
            $model->status = 1;
            $model->save();
            return responseSuccess($model, '添加成功');
        }catch (\Exception $e){
            return responseFail($e->getMessage());
        }
    }

    /**
     * describe：删除考试类型
     * delete
     * @param $data
     * @return null
     * 2025/6/13 - Mark
     */
    public function delete($data)
    {
        $model = ExamTypeModel::find($data['id']);
        if (!$model) {
            return responseFail('不存在该考试');
        }
        $model->delete();
        return responseSuccess($model, '删除成功');
    }

    /**
     * describe：修改考试类型
     * edit
     * @param $data
     * @return null
     * 2025/6/13 - Mark
     */
    public function edit($data)
    {
        $model = ExamTypeModel::find($data['id']);
        if (!$model) {
            return responseFail('不存在该考试类型');
        }
        //判断同业务是否存在考试类型
        $isExist = (new ExamTypeModel())->where('project_id', $data['project_id'])->where('course_id', $data['course_id'])->where('id', '<>', $data['id'])->first();
        if ($isExist) {
            return responseFail('该课程已存在考试');
        }
        $model->name = $data['name'];
        $model->project_id = $data['project_id'];
        $model->course_id = $data['course_id'];
        $model->status = $data['status'] ?? 1;
        $model->save();
        return responseSuccess($model, '修改成功');
    }

}