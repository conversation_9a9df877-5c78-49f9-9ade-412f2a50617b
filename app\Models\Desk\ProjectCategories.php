<?php

namespace App\Models\Desk;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProjectCategories extends Model
{
    use HasFactory;
    protected $connection = 'mysql_ai_desk';
    protected $table = 'cna_project_categories';

    protected $fillable = [
        'projectCategoriesID',
        'projectCategoriesState',
        'projectCategoriesNameZH',
        'projectCategoriesNameZT',
        'projectCategoriesNameEN',
        'projectCategoriesNameMS',
        'createUser',
        'createRole',
        'editTime',
        'type',
    ];

    //关联子项目
    public function projectServiceData(){
        return $this->hasMany(ProjectServiceData::class,'projectId','projectCategoriesID');
    }

    protected function serializeDate(\DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s'); // 自定义时间格式
    }
}