<?php
namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\ActiveLog;
use App\Models\CompanyGsp;
use App\Models\GspRefundModel;
use App\Models\IcbcOrderModel;
use App\Models\IcbcRefund;
use App\Models\PaymentModel;
use App\Services\ICBC\ICBCService;
use App\Services\OssService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class PaymentController extends Controller
{

    /**
     * 合伙人应收明细列表
     * @return void
     */
    public function in(Request $request)
    {

    }

    /**
     * 合伙人应付明细列表
     * @param Request $request
     * @return void
     */
    public function out(Request $request){
        $body = $request->all();
        $pageSize = $body['pageSize']?? 20;
        $page = $body['page']?? 1;
        $start_date = $body['start_date'] ?? date('Y-m-01 00:00:00');  // 开始时间
        $end_date = $body['end_date'] ?? date('Y-m-d H:i:s');  // 结束时间
        $profileID = $body['profileID']; //  合伙人ID
        $profilePartnerCode = $body['profilePartnerCode']; //  合伙人编码
        $groupId = $body['groupId']; //  合伙人角色(1联盟合伙人; 2管理合伙人; 3三三制)
        $projectId = $body['projectId']; // 项目ID
        $income_type = $body['income_type'];  // 收益类型(A,B,C,D,E)
        $divide_type = $body['divide_type']; // 分成类型(1收益分成;2管理津贴;3运营津贴)
        $check = $body['check']; // 审核状态(0暂存;1审核中;2已审核)

        $list = PaymentModel::query()
                ->where('type', 1)
                ->when($profileID != '', function ($query) use ($profileID) {
                    $query->where('profileID', $profileID);

                })->when($projectId != '', function ($query) use ($projectId) {
                    $query->where('projectId', $projectId);

                })->when($income_type != '', function ($query) use ($income_type) {
                    $query->where('income_type', $income_type);

                })->when($divide_type != '', function ($query) use ($divide_type) {
                    $query->where('divide_type', $divide_type);

                })->when($check != '', function ($query) use ($check) {
                    $query->where('check', $check);

                })->when($profilePartnerCode != '', function ($query) use ($profilePartnerCode) {
                    $query->whereHas('profile', function ($query) use ($profilePartnerCode) {
                        $query->where('profilePartnerCode', $profilePartnerCode);
                    });

                })->when($groupId != '', function ($query) use ($groupId) {
                    $query->whereHas('profile', function ($query) use ($groupId) {
                        if ($groupId == 1) { // 联盟合伙人
                            $query->where('pre_id', '>',0)->where('team_group', 0);
                        } else if ($groupId == 2) { // 管理合伙人
                            $query->where('pre_id', 0)->where('team_group', 0);
                        } else if ($groupId == 3) { // 运营津贴
                            $query->where('team_group', '>', 0);
                        }

                    });

                })->where('createtime', '>=', $start_date)
                ->where('createtime', '<=', $end_date)
                ->orderBy('id', 'asc')->paginate($pageSize);

        $items = $list->items();

        // 汇总
        $total = [
            'totalFee' => 0,
            'totalDividePercent' => 0,
            'totalDivideAmount' => 0,
            'totalProxyFax' => 0,
            'totalDivideProfit' => 0,
        ];

        // 遍历
        if ($items) {
            foreach ($items as $key => $item) {

                $total['totalFee']  = bcadd($total['totalFee'], $item->fee, 2);
                $total['totalDividePercent'] = bcadd($total['totalDividePercent'], $item->divide_percent, 2);
                $total['totalDivideAmount'] = bcadd($total['totalDivideAmount'], $item->divide_amount, 2);
                $total['totalProxyFax'] = bcadd($total['totalProxyFax'], $item->proxy_fax, 2);
                $total['totalDivideProfit'] = bcadd($total['totalDivideProfit'], $item->divide_profit, 2);

                $item->fee = number_format($item->fee, 2);
                $item->divide_percent = bcmul($item->divide_percent,100, 4).'%';
                $item->divide_amount = number_format($item->divide_amount, 2);
                $item->proxy_fax = number_format($item->proxy_fax, 2);
                $item->divide_profit = number_format($item->divide_profit, 2);
                $items[$key] = $item;
            }
        }


        $paginate = [
            'page_size' => $pageSize,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate', 'total'));

    }

    /**合伙人月结应付汇总
     * @param Request $request
     * @return void
     */
    public function totalOut(Request $request)
    {
        $body = $request->all();
        $pageSize = $body['pageSize']?? 20;
        $page = $body['page']?? 1;
        $start_date = $body['start_date'] ?? date('Y-m-01 00:00:00');  // 开始时间
        $end_date = $body['end_date'] ?? date('Y-m-d 23:59:59');  // 结束时间

        // 本期应付
        $list = PaymentModel::query()
            ->where('type', 1)
            ->where('createtime', '>=', $start_date)
            ->where('createtime', '<=', $end_date)
            ->select('profileID',DB::raw('SUM(divide_profit) as sum_divide_profit'))
            ->groupBy('profileID')
            ->paginate($pageSize);

        $items = $list->items();

        // 本期已付
        $arrPaid = PaymentModel::query()
            ->where('type', 1)
            ->where('put_time', '>=', $start_date)
            ->where('put_time', '<=', $end_date)
            ->select('profileID',DB::raw('SUM(put_amount) as sum_put_amount'))
            ->groupBy('profileID')->pluck('sum_put_amount', 'profileID');



        // 汇总
        $total = [
            'total_divide_profit' => 0, // 本期应付
            'total_put_amount' => 0,  // 本期已付
            'total_put_total' => 0, // 累计已付
            'total_start_balance' => 0,  // 期初余额
            'total_end_balance' => 0,  // 期末余额
        ];

        // 遍历
        if ($items) {
            foreach ($items as $key => $item) {

                if(!isset( $arrPaid[$item['profileID']])) {
                    $arrPaid[$item['profileID']] = 0;
                }
                
                // 本期已付
                $item['sum_put_amount'] = $arrPaid[$item['profileID']];
                // 累计已付
                $item['put_total'] = PaymentModel::getPutTotal($item['profileID']);
                // 期初余额
                $item['start_balance'] = PaymentModel::startBalance($start_date, $item['profileID']);
                // 期末余额
                $item['end_balance'] = bcsub( $arrPaid[$item['profileID']], $item['sum_divide_profit'],2);
                $item['end_balance'] = bcsub(  $item['start_balance'],$item['end_balance'],2);

                $total['total_divide_profit']  = bcadd($total['total_divide_profit'], $item['sum_divide_profit'], 2);
                $total['total_put_amount'] = bcadd($total['total_put_amount'], $item['sum_put_amount'], 2);
                $total['total_start_balance'] = bcadd($total['total_start_balance'],$item['start_balance'], 2);
                $total['total_end_balance'] = bcadd($total['total_end_balance'],$item['end_balance'], 2);
                $total['total_put_total'] = bcadd($total['total_put_total'], $item['put_total'], 2);


                $items[$key] = $item;
            }
        }

        $paginate = [
            'page_size' => $pageSize,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate', 'total'));

    }

    /**
     * 加盟费应收明细
     * @param Request $request
     * @return void
     */
    public function joinDetail(Request $request)
    {
        $body = $request->all();
        $pageSize = $body['pageSize']?? 40;
        $page = $body['page']?? 1;
        $start_date = $body['start_date'] ?? '';  // 开始时间
        $end_date = $body['end_date'] ?? '';  // 结束时间

        // 加盟费应收明细
        $list = PaymentModel::query()->with('projectContent')
            ->where('type', 2)
            ->where('profileID', '>', 10)
            ->where('profileID', '<>', 15)
            ->where('projectId', 20)
            ->when($start_date != '', function ($query) use ($start_date) {
                $query->where('createtime', '>=', $start_date);
            })->when($end_date != '', function ($query) use ($end_date) {
                $query->where('createtime', '<=', $end_date);
            })
            ->paginate($pageSize);

        $items = $list->items();

        // 汇总
        $total = [
            'total_fee' => 0, // 应收金额合计
            'total_put_amount' => 0,  // 收款金额合计
        ];

        if ($items) {
            foreach ($items as $key => $item) {
                $item['detail_name'] = $item->projectContent['title_zh'];
                unset( $item->projectContent);
                $items[$key] = $item;
                
                $total['total_fee'] = bcadd($total['total_fee'], $item['fee'], 2);
                $total['total_put_amount'] = bcadd($total['total_put_amount'], $item['put_amount'], 2);
            }
        }

        $paginate = [
            'page_size' => $pageSize,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate', 'total'));
    }

    /**
     * 加盟费应收汇总
     * @param Request $request
     * @return void
     */
    public function joinTotal(Request $request)
    {

        $body = $request->all();
        $pageSize = $body['pageSize']?? 40;
        $page = $body['page']?? 1;
        $start_date = $body['start_date'] ?? '';  // 开始时间
        $end_date = $body['end_date'] ?? '';  // 结束时间

        // 合伙人加盟费汇总
        $list = PaymentModel::query()
            ->where('type', 2)
            ->where('projectId', 20)
            ->where('profileID', '>', 10)
            ->where('profileID', '<>', 15)
            ->when($start_date != '', function ($query) use ($start_date) {
                $query->where('paytime', '>=', $start_date);
            })->when($end_date != '', function ($query) use ($end_date) {
                $query->where('paytime', '<=', $end_date);
            })
            ->select('profileID',DB::raw('SUM(fee) as sum_fee'), DB::raw('SUM(put_amount) as sum_put_amount'))
            ->groupBy('profileID')
            ->orderBy('sum_fee', 'desc')
            ->paginate($pageSize);

        $items = $list->items();

        // 累计已收
        $arrPutAmount = PaymentModel::query()
            ->where('type', 2)
            ->where('profileID', '>', 10)
            ->where('profileID', '<>', 15)
            ->where('projectId', 20)
            ->select('profileID',DB::raw('SUM(put_amount) as sum_put_amount'))
            ->groupBy('profileID')->pluck('sum_put_amount', 'profileID');

        // 汇总
        $total = [
            'total_fee' => 0, // 本期应收合计
            'total_put_amount' => 0,  // 本期已收合计
            'total_fee_total' => 0, // 累计已收
            'total_start_balance' => 0,  // 期初余额
            'total_end_balance' => 0,  // 期末余额
        ];

        if ($items) {
            foreach ($items as $key => $item) {
                // 累计已收
                $item['sum_fee_count'] = $arrPutAmount[$item['profileID']];
                // 期末余额
                $item['end_balance'] = bcsub(  $item['sum_fee'], $item['sum_put_amount'],2);

                // 本期应收合计
                $total['total_fee'] = bcadd($total['total_fee'], $item['sum_fee'], 2);
                // 本期已收合计
                $total['total_put_amount'] = bcadd($total['total_put_amount'], $item['sum_put_amount'], 2);
                // 累计已收合计
                $total['total_fee_total']  = bcadd($total['total_fee_total'], $item['sum_fee_count'], 2);
                // 期末余额合计
                $total['total_end_balance'] = bcadd($total['total_end_balance'], $item['end_balance'], 2);

                $items[$key] = $item;
            }
        }

        $paginate = [
            'page_size' => $pageSize,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate', 'total'));
    }

    public function uploadJoinInvoice(Request $request)
    {
        $user = $request->attributes->get('user');
        $body = $request->all();
        $validator = Validator::make($request->all(), [
            'id'            => 'required',
            'file'          => 'required',
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        // 判断是否上传过
        $info = PaymentModel::find($body['id']);
        if (!$info) {
            return responseFail(__('info no exist'));
        }

        if (!in_array($info['check'], [0,3])) {
            return responseFail(__('param error', ['param'=>'status'])); // 状态错误
        }

        // 发票文件
        $file = $request->file('file');
        $invoice_file = '';
        if ($file) {
            $resource = OssService::upload($file);
            //$resource = $file->store('files/invoice', 'public');
            $resource && $invoice_file = $resource;

        }

        $result = PaymentModel::where('id', $body['id'])->update([
            'invoice_file' => $invoice_file,
            'updated_at'   => date('Y-m-d H:i:s'),
            'check'        => 2,  // 已审核
            'check_time'   => date('Y-m-d H:i:s')  // 审核时间
        ]);

        if ($result !== false) {
            // 记录日志
            ActiveLog::log($user['profileID'], ActiveLog::ADMIN_API_V1_CREATE, ActiveLog::ADMIN_API_V1_PAYMENT_INVOICE,
                $body['id'], $resource, ActiveLog::SYSTEM_CNA_ADMIN);

        }

        return responseSuccess();

    }

    /**
     * 退款列表
     * @return void
     */
    public function refund(Request $request)
    {
        $body = $request->validate([
            'page_size' => [ 'integer', 'min:1'],
            'start_date' => ['date_format:Y-m-d', 'required_with:end_date'],
            'end_date' => ['date_format:Y-m-d', 'required_with:start_date'],
            'status' => ['integer', Rule::in(IcbcRefund::STATUS_ARR)],
        ]);
        $pageSize = $body['page_size'] ?? 10;
        $query = IcbcRefund::query();
        if (isset($body['start_date'])) {
            if ($body['start_date'] > $body['end_date']) {
                return responseFail();
            }
            $startDate = $body['start_date'];
            $endDate = $body['end_date'];
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }
        if (isset($body['status'])) {
            $query->where('status', $body['status']);
        }
        $data = $query->orderBy('id', 'desc')->paginate($pageSize);

        return responseSuccess($data);
    }

    /**
     * 确认退款
     * @param Request $request
     * @return void
     */
    public function checkRefund(Request $request)
    {
        $body = $request->validate([
            'id' => ['required', 'integer', 'min:1'],
            'status' => ['required','integer', Rule::in([IcbcRefund::STATUS_APPROVED, IcbcRefund::STATUS_REJECT])],
            'remark' => ['string']
        ]);
        $remark = isset($body['remark'])?$body['remark']:'';
        $info = IcbcRefund::find($body['id']);
        if (empty($info)) {
            return responseFail(__('info no exist'));
        }
        if ($body['status'] == IcbcRefund::STATUS_APPROVED) { // 同意退款
            // 调用退款接口
            $ret = ICBCService::returnQRcodeScanned('', $info['order_id'], $info['reason'], $info['amount']);
            if ($ret)  {

                try {
                    // 开启事务
                    DB::beginTransaction();

                    // step1 更改为退款状态
                    $gsp_id = IcbcOrderModel::where('order_id', $info['order_id'])->value('obj_id');
                    CompanyGsp::where('id', $gsp_id)->update(['pay_one_return' => 1]);

                    // step2 更新退款状态
                    $info->status = IcbcRefund::STATUS_APPROVED;
                    $info->remark = $remark;
                    $info->save();

                    // 提交
                    DB::commit();
                } catch (\Exception $e) {
                    DB::rollBack();
                    return responseFail();
                }

            } else {
                return responseFail($ret);
            }
        } else { // 拒绝退款
            // 更新退款状态
            $info->status = IcbcRefund::STATUS_REJECT;
            $info->remark = $remark;
            $info->save();
        }

        return responseSuccess();

    }
}