<?php

namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Services\ExamQuestionsServices;
use Illuminate\Http\Request;
use App\Models\ExamQuestionsModel;

class ExamQuestionsController extends Controller
{
    public function __construct(ExamQuestionsServices $examQuestionsServices){
        $this->services = $examQuestionsServices;
    }

    public function index(Request $request)
    {
        $param = $request->validate([
            'page'              => 'bail|nullable|integer',
            'page_size'        => 'bail|nullable|integer',
            'exam_id'         => 'bail|nullable|string',
            'get_type'         => 'bail|nullable|string'
        ]);
        return responseSuccess($this->services->getList($param));
    }

    /**
     * 验证问题数据
     * @param Request $request
     * @return void
     */
    private function validateQuestion(Request $request)
    {
        $request->validate([
            'exam_id' => 'required|integer',
            'type' => 'required|integer|in:' . implode(',', ExamQuestionsModel::TYPE_ALL),
            'question' => 'required|string',
            'score' => 'nullable|integer',
            'project_service_data_id' => 'nullable|exists:mysql_ai_desk.cna_project_service_data,id',
            'option' => ['required', 'array', function ($attribute, $value, $fail) use ($request) {
                $hasAnswer = false;
                $answerCount = 0;
                foreach ($value as $item) {
                    if (!isset($item['option_name']) || !is_string($item['option_name'])) {
                        $fail('选项格式不正确，缺少选项字段');
                        return;
                    }
                    if (!isset($item['is_answer']) || !in_array($item['is_answer'], [0, 1])) {
                        $fail('选项格式不正确，每个选项必须包含 is_answer 字段且值为 0 或 1');
                        return;
                    }
                    if ($item['is_answer'] == 1) {
                        $hasAnswer = true;
                        $answerCount++;
                    }
                }
                if (!$hasAnswer) {
                    $fail('必须设置一个正确答案');
                    return;
                }
                if ($request->type == ExamQuestionsModel::TYPE_RADIO && $answerCount > 1) {
                    $fail(ExamQuestionsModel::TYPE_REMARK[$request->type].'只能有一个正确答案');
                }
            }],
        ]);
    }

    /**
     * 获取问题数据
     * @param Request $request
     * @return array
     */
    private function getQuestionData(Request $request)
    {
        return [
            'exam_id' => $request->exam_id,
            'type' => $request->type,
            'question' => $request->question,
            'option' => $request->option,
            'score' => $request->score,
        ];
    }

    public function store(Request $request)
    {
        $this->validateQuestion($request);
        $this->services->save($this->getQuestionData($request));
        return responseSuccess();
    }

    public function update($id, Request $request)
    {
        if (!is_numeric($id) || !is_int((int)$id)) {
            return responseFail('ID必须是整数');
        }
        $this->validateQuestion($request);
        $this->services->save($this->getQuestionData($request), (int)$id);
        return responseSuccess();
    }

    public function destroy($id)
    {
        if (!is_numeric($id) || !is_int((int)$id)) {
            return responseFail('ID必须是整数');
        }
        $this->services->destroy((int)$id);
        return responseSuccess();
    }
}
