<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class IcbcRefund extends Model
{
    protected $table = 'cna_icbc_refund';
    protected $guarded = [];

    const STATUS_PEDDING = 0; //待处理
    const STATUS_APPROVED = 1; //已批准
    const STATUS_REJECT = 2; //拒绝

    const STATUS_ARR = [
        self::STATUS_PEDDING,
        self::STATUS_APPROVED,
        self::STATUS_REJECT,
    ];
}
